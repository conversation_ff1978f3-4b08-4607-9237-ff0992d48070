Module Name: FluxStore
Author: Inspire UI
Current Version: 2.4.0
Release Date: 17/05/2025

Changelog:
- [Version 2.4.0] - [17/05/2025]
  - [Feature]: Support get features in product details API
  - [Feature]: Add custom get feature API
  - [Feature]: Support filter product by multi attributes and features
  - [Feature]: Add get min-max price API
  - [Fix]: Fix not return correct language
  - [Feature]: Support hide invisible attributes and features if no products are found

- [Version 2.3.3] - [06/06/2024]
  - [Fix]: Not show all categories
  - [Fix]: Correct order status

- [Version 2.3.0] - [23/04/2024]
  - [Feature]: New API module

- [Version 2.2.1] - [27/11/2023]
  - [Feature]: Improve sort products by name, date_add and price (ASC or DESC)
  - [Feature]: Supports searching product with keywords in description and meta data

- [Version 2.2.0] - [16/11/2023]
  - [Fix]: Fix payment
  - [Fix]: Fix warning back office header
