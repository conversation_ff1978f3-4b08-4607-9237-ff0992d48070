<?php

/**
 * 2007-2022 PayPal
 *
 *  NOTICE OF LICENSE
 *
 *  This source file is subject to the Academic Free License (AFL 3.0)
 *  that is bundled with this package in the file LICENSE.txt.
 *  It is also available through the world-wide-web at this URL:
 *  http://opensource.org/licenses/afl-3.0.php
 *  If you did not receive a copy of the license and are unable to
 *  obtain it through the world-wide-web, please send an email
 *  to <EMAIL> so we can send you a copy immediately.
 *
 *  DISCLAIMER
 *
 *  Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 *  versions in the future. If you wish to customize PrestaShop for your
 *  needs please refer to http://www.prestashop.com for more information.
 *
 *  <AUTHOR> PayPal
 *  <AUTHOR> ecommerce <<EMAIL>>
 *  @copyright PayPal
 *  @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 */

/**
 * Manage errors.
 */

use PrestaShop\PrestaShop\Adapter\Product\PriceFormatter;
use PrestaShop\PrestaShop\Core\Checkout\TermsAndConditions;
use PrestaShop\PrestaShop\Core\Foundation\Templating\RenderableProxy;
use Symfony\Component\Security\Core\User\User;

class CustomapiPaymentModuleFrontController extends ModuleFrontController
{
    public function authentication()
    {
        $id_user = Tools::getValue('customer');
        $this->context->updateCustomer(new Customer($id_user));
        $this->context->cookie->id_customer = $id_user;
    }

    public function addNewCart()
    {
        $cart = new Cart();
        $user = new Customer($this->context->customer->id);
        $id_lang = Configuration::get('PS_LANG_DEFAULT');
        $id_carrier = null;
        $id_address = Address::getFirstCustomerAddressId($user->id);
        if ($id_address) {
            $cart->id_address_delivery = (int) $id_address;
            $cart->id_address_invoice = (int) $cart->id_address_delivery;
        } else {
            $cart->id_address_delivery = 0;
            $cart->id_address_invoice = 0;
        }
        $carriers = Carrier::getCarriers($id_lang);
        if (!empty($carriers)) {
            $carrier = $carriers[0];
            $id_carrier = $carrier['id_carrier'];
        }
        $cart->id_carrier = $id_carrier;
        $cart->id_shop = Context::getContext()->shop->id;
        $cart->id_shop_group = Context::getContext()->shop->id_shop_group;
        $cart->id_customer = $user->id;
        $cart->id_lang = $id_lang;
        $cart->secure_key = $user->secure_key;
        $cart->id_currency = 1;
        $deliveryOption = [$cart->id_address_delivery => $id_carrier . ','];
        $cart->setDeliveryOption($deliveryOption);
        $cart->gift_message = '';
        $cart->gift = 0;
        $cart->mobile_theme = 0;
        $cart->add();
        return $cart;
    }

    public function addProductToCart()
    {
        $cart = new Cart();
        $idCart = (int) Cart::lastNoneOrderedCart($this->context->customer->id);
        if ($idCart != false) {
            $cart = new Cart($idCart);
            $cart->deleteAssociations();
        } else {
            $cart = $this->addNewCart();
        }
        $matches = json_decode(Tools::getvalue('products'));
        $quantity = json_decode(Tools::getvalue('quantity'));
        $attribute = json_decode(Tools::getvalue('attribute'));
        $products = array();
        for ($i = 0; $i < count($matches); $i++) {
            $product_value['id_product'] = $matches[$i];
            $product_value['id_product_attribute'] = strval($attribute[$i]) != '-1' ? (int)$attribute[$i] : 0;
            $product_value['id_address_delivery'] = $cart->id_address_delivery;
            $product_value['quantity'] = $quantity[$i];
            if ((int)$quantity[$i] < 1) continue;
            $products[] = $product_value;
        }
        if (!empty($products)) {
            $cart->setWsCartRows($products);
        }
        $this->context->cart = $cart;
        $this->context->cookie->id_cart = $cart->id;
    }

    /**
     * @see FrontController::initContent()
     */
    public function initContent()
    {
        try {
            $this->authentication();
            $this->addProductToCart();
            $orderLink = $this->context->link->getPageLink('order');
        } catch (Exception $e) {
            $error = `Invalid payment ` . $e->getMessage() . Configuration::get('PS_COUNTRY_DEFAULT');
        }
        Context::getContext()->smarty->assign([
            'error_msg' => $error ?? '',
            'order_link' => $orderLink ?? '',
        ]);
        $this->setTemplate('module:customapi/controllers/front/payment/payment.tpl');
    }
}
