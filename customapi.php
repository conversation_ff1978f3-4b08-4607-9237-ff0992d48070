<?php

/**
 * 2007-2020 PrestaShop
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to http://www.prestashop.com for more information.
 *
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright 2007-2020 PrestaShop SA
 *  @license   http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 *  International Registered Trademark & Property of PrestaShop SA
 */
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementShipping.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementPayment.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementOrder.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementRegister.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementProduct.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementAttribute.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementAddress.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementOrderdetail.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/WebserviceSpecificManagementDebug.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/WebserviceSpecificManagementProducts.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/WebserviceSpecificManagementCombinations.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/WebserviceSpecificManagementProductOptionValues.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/WebserviceSpecificManagementProductOptions.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/categories/WebserviceSpecificManagementCategories.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/shipping/WebserviceSpecificManagementCarriers.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/payments/WebserviceSpecificManagementPaymentMethods.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/orders/WebserviceSpecificManagementOrders.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/orders/WebserviceSpecificManagementOrderHistories.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/coupons/WebserviceSpecificManagementCartRules.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/authentications/WebserviceSpecificManagementSignIn.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/authentications/WebserviceSpecificManagementSignUp.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/authentications/WebserviceSpecificManagementUpdateUser.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/WebserviceSpecificManagementProductFeatures.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/WebserviceSpecificManagementProductFeatureValues.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/WebserviceSpecificManagementProductPriceRange.php';


if (!defined('_PS_VERSION_')) {
    exit;
}

class Customapi extends Module
{
    protected $config_form = false;

    public function __construct()
    {
        $this->name = 'customapi';
        $this->tab = 'administration';
        $this->version = '2.4.0';
        $this->author = 'Inspire UI';
        $this->need_instance = 1;

        /**
         * Set $this->bootstrap to true if your module is compliant with bootstrap (PrestaShop 1.6)
         */
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('FluxStore WebService');
        $this->description = $this->l('Adds controller to support the REST architecture in order to be available on as many platforms as possible');

        $this->confirmUninstall = $this->l('Are you sure want to uninstall?');

        $this->ps_versions_compliancy = array('min' => '1.6', 'max' => _PS_VERSION_);
    }

    /**
     * Don't forget to create update methods if needed:
     * http://doc.prestashop.com/display/PS16/Enabling+the+Auto-Update
     */
    public function install()
    {
        Configuration::updateValue('CUSTOMAPI_SEND_MAIL', false);
        Configuration::updateValue('CUSTOMAPI_ENABLE_TAX', true);
        Configuration::updateValue('CUSTOMAPI_ORDER_STOCK', '99');

        return parent::install() &&
            $this->registerHook('displayHeader') &&
            $this->registerHook('displayBackOfficeHeader') &&
            $this->registerHook('addWebserviceResources');
    }

    public function uninstall()
    {
        Configuration::deleteByName('CUSTOMAPI_SEND_MAIL');
        Configuration::deleteByName('CUSTOMAPI_ENABLE_TAX');
        Configuration::deleteByName('CUSTOMAPI_ORDER_STOCK');
        return parent::uninstall();
    }

    /**
     * Load the configuration form
     */
    public function getContent()
    {
        /**
         * If values have been submitted in the form, process.
         */
        if (((bool) Tools::isSubmit('submitCustomapiModule')) == true) {
            $this->postProcess();
        }

        $this->context->smarty->assign('module_dir', $this->_path);

        $output = $this->context->smarty->fetch($this->local_path . 'views/templates/admin/configure.tpl');

        return $this->renderForm();
    }

    /**
     * Create the form that will be displayed in the configuration of your module.
     */
    protected function renderForm()
    {
        $helper = new HelperForm();

        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitCustomapiModule';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            . '&configure=' . $this->name . '&tab_module=' . $this->tab . '&module_name=' . $this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->tpl_vars = array(
            'fields_value' => $this->getConfigFormValues(), /* Add values for your inputs */
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        );

        return $helper->generateForm(array($this->getConfigForm()));
    }

    /**
     * Create the structure of your form.
     */
    protected function getConfigForm()
    {
        return array(
            'form' => array(
                'legend' => array(
                    'title' => $this->l('Fluxstore Settings'),
                    'icon' => 'icon-cogs',
                ),
                'input' => array(
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Send mail'),
                        'name' => 'CUSTOMAPI_SEND_MAIL',
                        'is_bool' => true,
                        'desc' => $this->l('Send a mail to user when create an order'),
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ),
                            array(
                                'id' => 'active_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            )
                        ),
                    ),
                    array(
                        'type' => 'switch',
                        'label' => $this->l('Enable taxes'),
                        'name' => 'CUSTOMAPI_ENABLE_TAX',
                        'is_bool' => true,
                        'desc' => $this->l('Enable taxes price for products'),
                        'values' => array(
                            array(
                                'id' => 'active_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ),
                            array(
                                'id' => 'active_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            )
                        ),
                    ),
                    array(
                        'col' => 3,
                        'type' => 'text',
                        'prefix' => '<i class="icon icon-cogs"></i>',
                        'desc' => $this->l('Enter your shop id'),
                        'name' => 'CUSTOMAPI_SHOP_ID',
                        'label' => $this->l('Shop ID'),
                    ),
                    array(
                        'col' => 3,
                        'type' => 'text',
                        'prefix' => '<i class="icon icon-cogs"></i>',
                        'desc' => $this->l('Enter your order stock amount'),
                        'name' => 'CUSTOMAPI_ORDER_STOCK',
                        'label' => $this->l('Order stock'),
                    ),
                    // array(
                    //     'type' => 'password',
                    //     'name' => 'CUSTOMAPI_ACCOUNT_PASSWORD',
                    //     'label' => $this->l('Password'),
                    // ),
                ),
                'submit' => array(
                    'title' => $this->l('Save'),
                ),
            ),
        );
    }

    /**
     * Set values for the inputs.
     */
    protected function getConfigFormValues()
    {
        return array(
            'CUSTOMAPI_SEND_MAIL' => Configuration::get('CUSTOMAPI_SEND_MAIL', true),
            'CUSTOMAPI_ENABLE_TAX' => Configuration::get('CUSTOMAPI_ENABLE_TAX', true),
            'CUSTOMAPI_SHOP_ID' => Configuration::get('CUSTOMAPI_SHOP_ID', null),
            'CUSTOMAPI_ORDER_STOCK' => Configuration::get('CUSTOMAPI_ORDER_STOCK', null),
            'CUSTOMAPI_ACCOUNT_EMAIL' => Configuration::get('CUSTOMAPI_ACCOUNT_EMAIL', '<EMAIL>'),
            'CUSTOMAPI_ACCOUNT_PASSWORD' => Configuration::get('CUSTOMAPI_ACCOUNT_PASSWORD', null),
        );
    }

    /**
     * Save form data.
     */
    protected function postProcess()
    {
        $form_values = $this->getConfigFormValues();

        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }
    }

    /**
     * Add the CSS & JavaScript files you want to be loaded in the BO.
     */
    public function hookBackOfficeHeader()
    {
        if (Tools::getValue('module_name') == $this->name) {
            $this->context->controller->addJS($this->_path . 'views/js/back.js');
            $this->context->controller->addCSS($this->_path . 'views/css/back.css');
        }
    }

    /**
     * Add the CSS & JavaScript files you want to be added on the FO.
     */
    public function hookHeader()
    {
        $this->context->controller->addJS($this->_path . '/views/js/front.js');
        $this->context->controller->addCSS($this->_path . '/views/css/front.css');
    }

    public function hookAddWebserviceResources()
    {

        return array(

            'shipping' => array(
                'description' => 'Shipping Method',
                'specific_management' => true,
            ),
            'payment' => array(
                'description' => 'Payment Module',
                'specific_management' => true,
            ),
            'order' => array(
                'description' => 'Create Order',
                'specific_management' => true,
            ),
            'register' => array(
                'description' => 'Create Customer Account',
                'specific_management' => true,
            ),
            'product' => array(
                'description' => 'Get List Product',
                'specific_management' => true,
            ),
            'attribute' => array(
                'description' => 'Get Product Attribute',
                'specific_management' => true,
            ),
            'address' => array(
                'description' => 'Create or Get Address',
                'specific_management' => true,
            ),
            'orderdetail' => array(
                'description' => 'Create or Get Address',
                'specific_management' => true,
            ),
            'debug' => array(
                'description' => 'Debug your site',
                'specific_management' => true,
            ),
            'products' => array(
                'description' => 'products',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'combinations' => array(
                'description' => 'combinations',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'categories' => array(
                'description' => 'categories',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'carriers' => array(
                'description' => 'carriers',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'payment_methods' => array(
                'description' => 'payment_methods',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'orders' => array(
                'description' => 'orders',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'order_histories' => array(
                'description' => 'order_histories',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'product_option_values' => array(
                'description' => 'product_option_values',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'product_options' => array(
                'description' => 'product_options',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'cart_rules' => array(
                'description' => 'cart_rules',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'sign_in' => array(
                'description' => 'sign_in',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'sign_up' => array(
                'description' => 'sign_up',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'update_user' => array(
                'description' => 'update_user',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'product_features' => array(
                'description' => 'Get Product Features',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true,
            ),
            'product_feature_values' => array(
                'description' => 'Get Product Feature Values',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
            'product_price_range' => array(
                'description' => 'Get Min-Max Price of Products',
                'forbidden_method' => array('DELETE'),
                'specific_management' => true
            ),
        );
    }
}
