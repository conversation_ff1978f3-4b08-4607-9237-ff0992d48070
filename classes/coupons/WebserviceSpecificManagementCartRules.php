<?php
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';
class WebserviceSpecificManagementCartRules implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function getCartRules($page, $per_page, $search, $id_user)
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($per_page < 0) {
            $per_page = 1;
        }
        $start = ($page - 1) * $per_page;
        $query = '
        SELECT *
        FROM `' . _DB_PREFIX_ . 'cart_rule` c
        WHERE 1 AND 
        ' . (empty($search) ? 'c.`id_customer` IN (0' . (!empty($id_user) ? ', '.$id_user.'' : '') . ')' : '') . '
        ' . (!empty($search) ? 'c.`code` = \'' . ($search) . '\''.(!empty($id_user) ? ' 
        AND c.`id_customer` = '.$id_user.'' : '').'' : '') . '
        LIMIT ' . $start . ', ' . $per_page . '';
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
    }


    public function manage()
    {

        $objects_cart_rules = array();

        $objects_cart_rules['empty'] = new CartRule();

        $params = $this->wsObject->urlFragments;
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        $page = intval($params['page'] ?? 1);
        $per_page = intval($params['per_page'] ?? 20);
        $search = $params['search'] ?? null;
        $id_user = $params['id_user'] ?? null;
        $cart_rules = $this->getCartRules($page, $per_page, $search, $id_user);
        if (!empty($cart_rules)) {
            foreach ($cart_rules as $cart_rule) {
                $id = $cart_rule['id_cart_rule'];
                $item = new CartRule($id);
                $item->name = FluxUntils::getName($item->name, $id_lang);
                $objects_cart_rules[] = $item;
            }
        }
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_cart_rules, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
