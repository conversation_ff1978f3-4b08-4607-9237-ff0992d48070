<?php

include_once _PS_MODULE_DIR_ . 'customapi/classes/orders/FluxOrderHistory.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/orders/FluxOrder.php';
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';

class WebserviceSpecificManagementOrderHistories implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function getOrderHistories($page, $per_page, $id_order)
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($per_page < 0) {
            $per_page = 1;
        }
        $start = ($page - 1) * $per_page;
        $query = '
        SELECT *
        FROM `' . _DB_PREFIX_ . 'order_history` o
        WHERE 1
        ' . (!empty($id_order) ? ' AND o.`id_order` = ' . $id_order . '' : '') . '
        ORDER BY o.`date_add` DESC
        LIMIT ' . $start . ', ' . $per_page . '';
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
    }


    public function manage()
    {

        $objects_order_histories = array();

        $objects_order_histories['empty'] = new FluxOrderHistory();

        $params = $this->wsObject->urlFragments;
        $page = intval($params['page'] ?? 1);
        $per_page = intval($params['per_page'] ?? 20);
        $id_order = $params['id_order'] ?? null;
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        $order_histories = $this->getOrderHistories($page, $per_page, $id_order);
        foreach ($order_histories as $order_history) {
            $id = $order_history['id_order_history'];
            $item = new FluxOrderHistory($id);
            $order_state = new OrderState($item->id_order_state);
            $item->status = FluxUntils::getName($order_state->name, $id_lang);
            $objects_order_histories[] = $item;
        }
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_order_histories, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
