<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/orders/FluxOrder.php';
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';


class WebserviceSpecificManagementOrders implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;

    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }


    public function getCountryId($country_iso)
    {
        if (empty($country_iso)) return 0;
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_country`
            FROM `' . _DB_PREFIX_ . 'country`
            WHERE `iso_code` = \'' . $country_iso . '\'
            LIMIT 1'
        );
        if (empty($result)) return 0;
        return $result[0]['id_country'];
    }

    public function getStateId($id_state, $id_country)
    {
        if (empty($id_state)) return 0;
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_state`
            FROM `' . _DB_PREFIX_ . 'state`
            WHERE `id_state` = ' . $id_state . ' AND `id_country` = ' . $id_country . '
            LIMIT 1'
        );
        if (empty($result)) return 0;
        return $result[0]['id_state'];
    }

    public function getAddress($id_customer, $json)
    {
        $country = $json->country ?? null;
        $state = $json->state ?? null;
        $firstname = $json->firstname ?? '';
        $lastname = $json->lastname ?? '';
        $city = $json->city ?? '';
        $postcode = $json->postcode ?? '';
        $address1 = $json->address1 ?? '';
        $phone = $json->phone ?? '';
        $address2 = $json->address2 ?? '';
        $company = $json->company ?? '';
        $id_country = 0;
        if ($country != null) {
            $value = Country::getByIso($country);
            $id_country = $value != false ? $value : 0;
        }
        $id_state = 0;
        if ($state != null) {
            $value = State::getIdByIso($state);
            $id_state = $value != false ? $value : 0;
        }
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_address`
            FROM `' . _DB_PREFIX_ . 'address`
            WHERE `id_country` = ' . $id_country . ' AND 
            `id_state` = ' . $id_state . ' AND 
            `id_customer` = ' . $id_customer . ' AND
            `firstname` = \'' . $firstname . '\' AND `lastname` = \'' . $lastname . '\' AND
            `address1` = \'' . $address1 . '\' AND `city` = \'' . $city . '\' AND 
            `address2` = \'' . $address2 . '\' AND `company` = \'' . $company . '\' AND
            `postcode` = \'' . $postcode . '\' AND `phone` = \'' . $phone . '\'
            '
        );
        if (empty($result)) {
            $excuce = 'INSERT INTO `' . _DB_PREFIX_ . 'address` (alias, id_country, id_state, id_customer, firstname, lastname, address1, city, postcode, phone, phone_mobile, date_add, date_upd, address2, company)
            VALUES ("My address", ' . $id_country . ', ' . $id_state . ', ' . $id_customer . ', "' . $firstname . '", "' . $lastname . '", "' . $address1 . '", "' . $city . '", "' . $postcode . '", "' . $phone . '", "' . $phone . '", "' . date("Y-m-d H:i:s") . '", "' . date("Y-m-d H:i:s") . '", "' . $address2 . '", "' . $company . '")';
            $new_address = Db::getInstance()->execute($excuce);
            if ($new_address == true) {
                return Db::getInstance()->Insert_ID();
            }
            return false;
        };
        return current($result)['id_address'];
    }

    public function getGuestUser($json)
    {
        $email = $json->email ?? null;
        $firstname = $json->firstname ?? null;
        $lastname = $json->lastname ?? null;
        if ($email == null) return null;
        $email = strtolower($email);
        $customers = Customer::getCustomersByEmail($email);
        if (!empty($customers)) {
            $item = current($customers);
            if ($item['is_guest']) {
                return $item['id_customer'];
            }
        } else {
            $customer = new Customer();
            $customer->email = $email;
            $customer->firstname = $firstname ?? 'Guest';
            $customer->lastname = $lastname ?? 'User';
            $customer->setWsPasswd('12345678#@');
            $customer->id = null;
            $customer->is_guest = true;
            $customer->add();
            return $customer->id;
        }
        return null;
    }

    public function addToCart($json, Customer $user, $id_address)
    {
        $id_carrier = $json->id_carrier ?? 0;
        $id_cart_rule = $json->id_cart_rule ?? null;
        $cart = new Cart();
        $cart->id_address_delivery = $id_address;
        $cart->id_address_invoice = $id_address;
        $cart->id_carrier = $id_carrier;
        $cart->id_shop = Context::getContext()->shop->id;
        $cart->id_shop_group = Context::getContext()->shop->id_shop_group;
        $cart->id_customer = $user->id;
        $cart->id_lang = Configuration::get('PS_LANG_DEFAULT');
        $cart->secure_key = $user->secure_key;
        $cart->id_currency = 1;
        $deliveryOption = [$cart->id_address_delivery => $id_carrier . ','];
        $cart->setDeliveryOption($deliveryOption);
        $cart->gift_message = '';
        $cart->gift = 0;
        $cart->mobile_theme = 0;
        $cart->add();
        if (!empty($id_cart_rule)) {
            $value = $cart->addCartRule($id_cart_rule);
            if ($value) {
                $this->increaseCartRuleQuantity($id_cart_rule);
            }
        }
        $products = $json->line_items ?? [];
        $product_rows = array();
        if (is_array($products)) {
            for ($i = 0; $i < count($products); $i++) {
                $item = $products[$i];
                $variation_id = $item->variation_id ?? null;
                $id_product = $item->product_id ?? 0;
                $quantity = $item->quantity ?? 0;
                $product_value['id_product'] = $id_product;
                $product_value['id_product_attribute'] = $variation_id != null ? $variation_id : 0;
                $product_value['id_address_delivery'] = $id_address;
                $product_value['quantity'] = $quantity;
                $product_rows[] = $product_value;
            }
        }
        $cart->setWsCartRows($product_rows);
        return $cart;
    }

    public function increaseCartRuleQuantity($id_cart_rule)
    {
        $query = '
        UPDATE `' . _DB_PREFIX_ . 'cart_rule`
        SET `quantity`= quantity - 1
        WHERE `id_cart_rule` = \'' . $id_cart_rule . '\'
        ';
        Db::getInstance()->execute(
            $query
        );
    }

    public static function getCustomerOrders($id_customer, $page, $per_page)
    {
        $start = ($page - 1) * $per_page;
        $res = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS('
        SELECT o.*,
          (SELECT SUM(od.`product_quantity`) FROM `' . _DB_PREFIX_ . 'order_detail` od WHERE od.`id_order` = o.`id_order`) nb_products,
          (SELECT oh.`id_order_state` FROM `' . _DB_PREFIX_ . 'order_history` oh
           LEFT JOIN `' . _DB_PREFIX_ . 'order_state` os ON (os.`id_order_state` = oh.`id_order_state`)
           WHERE oh.`id_order` = o.`id_order` ' .
            ' ORDER BY oh.`date_add` DESC, oh.`id_order_history` DESC LIMIT 1) id_order_state
        FROM `' . _DB_PREFIX_ . 'orders` o
        WHERE o.`id_customer` = ' . (int) $id_customer .
            Shop::addSqlRestriction(Shop::SHARE_ORDER) . '
        GROUP BY o.`id_order`
        ORDER BY o.`date_add` DESC
        LIMIT ' . $start . ', ' . $per_page . '');
        if (!$res) {
            return [];
        }
        return $res;
    }


    public function manage()
    {

        $objects_orders = array();
        $params = $this->wsObject->urlFragments;
        $objects_orders['empty'] = new Order();
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $putresource = fopen('php://input', 'rb');
            $body = null;
            while ($putData = fread($putresource, 1024)) {
                $body .= $putData;
            }
            fclose($putresource);
            $json = json_decode($body);
            $id_customer = $json->id_customer ?? null;
            if ($id_customer == null) {
                $id_customer = $this->getGuestUser($json);
            }
            $user = new Customer($id_customer);
            if ($id_customer == null) {
                $this->wsObject->setError(400, 'The email is already used, please choose another one or sign in', 'auth/email');
            } else {
                $id_address = $this->getAddress($id_customer, $json);
                if ($id_address == false) {
                    $this->wsObject->setError(400, 'Can not get user address', 'auth/address');
                } else {
                    $cart = $this->addToCart($json, $user, $id_address);
                    $order = FluxOrder::createOrder($json, $cart, $user, $id_address);
                    if ($order->id != null) {
                        $objects_orders[] = $order;
                    }
                }
            }
        }
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $id_customer = $params['id_customer'] ?? null;
            $page = intval($params['page'] ?? 1);
            $per_page = intval($params['per_page'] ?? 20);
            if ($id_customer != null) {
                $values = $this->getCustomerOrders($id_customer, $page, $per_page);
                if (!empty($values)) {
                    foreach ($values as $value) {
                        $item = new FluxOrder($value['id_order']);
                        $item->lang = $id_lang;
                        $order_state = new OrderState($item->current_state);
                        $status = $order_state->name;
                        $item->status = 'processing';
                        if (gettype($status) == 'array') {
                            if (!empty(array_search('Payment accepted', $status))) {
                                $item->status = 'completed';
                            }
                            if (!empty(array_search('Shipped', $status)) || !empty(array_search('Delivered', $status))) {
                                $item->status = 'delivered';
                            }
                            if (!empty(array_search('Canceled', $status))) {
                                $item->status = 'canceled';
                            }
                            if (!empty(array_search('Refunded', $status))) {
                                $item->status = 'refunded';
                            }
                            if (!empty(array_search('On backorder (not paid)', $status))) {
                                $item->status = 'denied';
                            }
                        } else {
                            switch ($status) {
                                case 'Payment accepted':
                                    $item->status = 'completed';
                                    break;
                                case 'Shipped':
                                case 'Delivered':
                                    $item->status = 'delivered';
                                    break;
                                case 'Canceled':
                                    $item->status = 'canceled';
                                    break;
                                case 'Refunded':
                                    $item->status = 'refunded';
                                    break;
                                case 'Payment error':
                                    $item->status = 'failed';
                                    break;
                                case 'On backorder (not paid)':
                                    $item->status = 'denied';
                                    break;
                            }
                        }
                        if ($order_state->invoice) {
                            $item->status = 'completed';
                        }
                        $objects_orders[] = $item;
                    }
                }
            }
        }

        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_orders, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
