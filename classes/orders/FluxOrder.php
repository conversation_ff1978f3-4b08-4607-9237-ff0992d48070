<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/payments/FluxPayPal.php';
class FluxOrder extends Order
{
    public $status = null;
    public $lang;

    protected $webserviceParameters = [
        'objectMethods' => ['add' => 'addWs'],
        'objectNodeName' => 'order',
        'objectsNodeName' => 'orders',
        'fields' => [
            'status' => [
                'getter' => false,
                'setter' => false,
            ],
        ],
        'associations' => [
            'order_rows' => [
                'resource' => 'order_row', 'setter' => false, 'virtual_entity' => true,
                'fields' => [
                    'id' => [],
                    'product_id' => ['required' => true, 'xlink_resource' => 'products'],
                    'product_attribute_id' => ['required' => true],
                    'product_quantity' => ['required' => true],
                    'product_name' => ['setter' => false],
                    'product_reference' => ['setter' => false],
                    'product_ean13' => ['setter' => false],
                    'product_isbn' => ['setter' => false],
                    'product_upc' => ['setter' => false],
                    'product_price' => ['setter' => false],
                    'id_customization' => ['required' => false, 'xlink_resource' => 'customizations'],
                    'unit_price_tax_incl' => ['setter' => false],
                    'unit_price_tax_excl' => ['setter' => false],
                ],
            ],
            'shipping_address' => [
                'resource' => 'address',
                'fields' => [
                    'id_address' => [],
                    'id_country' => [],
                    'firstname' => [],
                    'lastname' => [],
                    'phone' => [],
                    'address1' => [],
                    'address2' => [],
                    'company' => [],
                    'postcode' => [],
                    'phone_mobile' => [],
                    'country' => [],
                    'city' => []
                ],
            ],
        ],
    ];

    public function getWsShippingAddress()
    {
        $result = Db::getInstance()->executeS('SELECT DISTINCT *, cl.name as country
        FROM `' . _DB_PREFIX_ . 'address` ad
        LEFT JOIN `' . _DB_PREFIX_ . 'country_lang` cl ON (ad.id_country = cl.id_country)
        WHERE '. !(empty($lang) ? 'cl.id_lang='.$this->lang.'' : '') .'
        ad.id_address = ' . (int) $this->id_address_delivery);
        return $result;
    }

    static public function setOrderStateByModule($module_name)
    {
        Tools::displayAsDeprecated();
        $id_order_state = Db::getInstance()->getValue('
        SELECT `id_order_state`
        FROM `' . _DB_PREFIX_ . 'order_state`
        WHERE `module_name` = \'' . $module_name . '\'');

        // returns false if there is no state
        if (!$id_order_state) {
            return 1;
        }
        return $id_order_state;
    }

    static public function addOrderHistory($id_order, $id_order_state) {
        try {
            $history = new OrderHistory();
            $history->id_order = $id_order;
            $history->id_order_state = $id_order_state;
            $history->id_employee = 0;
            $history->addWithemail();
        } catch (Exception $e) {
        }
    }

    static public function addInvoice(Order $order, $id_order_payment) {
        $invoice = new OrderInvoice();
        $invoice->id_order = $order->id;
        $invoice->number = 0;
        $invoice->total_paid_tax_excl = $order->total_paid_tax_excl;
        $invoice->total_paid_tax_incl = $order->total_paid_tax_incl;
        $invoice->total_products = $order->total_products;
        $invoice->total_products_wt = $order->total_products_wt;
        $invoice->total_shipping_tax_excl = $order->total_shipping_tax_excl;
        $invoice->total_shipping_tax_incl = $order->total_shipping_tax_incl;
        $invoice->shop_address = '';
        $invoice->add();
        if ($invoice->id != null) {
            $order->invoice_number = $invoice->id;
            $order->update();
            Db::getInstance()->insert(
                'order_invoice_payment',
                [
                    'id_order_invoice' => (int) $invoice->id,
                    'id_order_payment' => (int) $id_order_payment,
                    'id_order' => (int) $order->id,
                ]
            );
        }
    }

    static public function createOrder($json, Cart $cart, Customer $user, $id_address)
    {
        $order = new FluxOrder();
        $id_carrier = $json->id_carrier ?? 0;
        $id_currency = Currency::getIdByIsoCode($json->currency ?? 'USD');
        $order->id_carrier = $id_carrier;
        $order->id_customer = $user->id;
        $order->id_currency = $id_currency;
        $order->id_address_delivery = $id_address;
        $order->id_address_invoice = $id_address;
        $order->id_cart = $cart->id;
        $order->id_lang = Configuration::get('PS_LANG_DEFAULT');
        $order->conversion_rate = 1;
        $order->invoice_number = 0;
        $order->delivery_number = 0;
        $order->invoice_date = '0000-00-00 00:00:00';
        $order->delivery_date = '0000-00-00 00:00:00';
        $order->valid = 0;
        $order->mobile_theme = 0;
        $order->reference = Order::generateReference();
        $order->id_shop = Context::getContext()->shop->id;
        $order->id_shop_group = Context::getContext()->shop->id_shop_group;
        $order->gift_message = $cart->gift_message;
        $order->total_paid = $order->total_products;
        //load payment
        $module = Module::getInstanceByName($json->module ?? null);
        $order->payment = $module->displayName;
        $order->module = $module->name;
        //update total shipping
        $product_list = array_values($cart->getProducts());
        $currency = new Currency($id_currency);
        $computingPrecision = $currency->precision;
        $address = new Address($id_address);
        $default_country = new Country($address->id_country);
        $carrier = new Carrier($id_carrier);
        $order->total_products = Tools::ps_round(
            (float) $cart->getOrderTotal(false, Cart::ONLY_PRODUCTS, $product_list, $id_carrier),
            $computingPrecision
        );
        $order->total_products_wt = Tools::ps_round(
            (float) $cart->getOrderTotal(true, Cart::ONLY_PRODUCTS, $product_list, $id_carrier),
            $computingPrecision
        );
        $order->total_discounts_tax_excl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(false, Cart::ONLY_DISCOUNTS, $product_list, $id_carrier)),
            $computingPrecision
        );
        $order->total_discounts_tax_incl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(true, Cart::ONLY_DISCOUNTS, $product_list, $id_carrier)),
            $computingPrecision
        );
        $order->total_discounts = $order->total_discounts_tax_incl;

        $order->total_shipping_tax_excl = Tools::ps_round(
            (float) $cart->getPackageShippingCost($id_carrier, false, $default_country, $product_list, $default_country->id_zone),
            $computingPrecision
        );
        $order->total_shipping_tax_incl = Tools::ps_round(
            (float) $cart->getPackageShippingCost($id_carrier, true, $default_country, $product_list, $default_country->id_zone),
            $computingPrecision
        );
        $order->total_shipping = $order->total_shipping_tax_incl;

        if (null !== $carrier && Validate::isLoadedObject($carrier)) {
            $order->carrier_tax_rate = $carrier->getTaxesRate(new Address((int) $cart->{Configuration::get('PS_TAX_ADDRESS_TYPE')}));
        }

        $order->total_wrapping_tax_excl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(false, Cart::ONLY_WRAPPING, $product_list, $id_carrier)),
            $computingPrecision
        );
        $order->total_wrapping_tax_incl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(true, Cart::ONLY_WRAPPING, $product_list, $id_carrier)),
            $computingPrecision
        );
        $order->total_wrapping = $order->total_wrapping_tax_incl;

        $order->total_paid_tax_excl = Tools::ps_round(
            (float) $cart->getOrderTotal(false, Cart::BOTH, $product_list, $id_carrier),
            $computingPrecision
        );
        $order->total_paid_tax_incl = Tools::ps_round(
            (float) $cart->getOrderTotal(true, Cart::BOTH, $product_list, $id_carrier),
            $computingPrecision
        );
        $order->total_paid = $order->total_paid_tax_incl;
        $order->total_paid_real = $order->total_paid;
        $order->round_mode = Configuration::get('PS_PRICE_ROUND_MODE');
        $order->round_type = Configuration::get('PS_ROUND_TYPE');
        $order->current_state = FluxOrder:: setOrderStateByModule($module->name);
        $order->secure_key = $user->secure_key;
        $order->add();
        //insert order detail
        $orderDetail = new OrderDetail();
        $orderDetail->createList($order, $cart, 0, array_values($cart->getProducts()));
        //create order history
        FluxOrder::addOrderHistory($order->id, $order->current_state);
        //add paypal payment
        $id_payment = $json->id_payment ?? null;
        if (isset($id_payment)) {
            FluxPayPal::completeOrder($order, $cart, $id_payment);
        }
        return $order;
    }
}
