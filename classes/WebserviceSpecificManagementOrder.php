<?php


class WebserviceSpecificManagementOrder implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;



    public function setUrlSegment($segments)

    {

        $this->urlSegment = $segments;

        return $this;
    }



    public function getUrlSegment()

    {

        return $this->urlSegment;
    }

    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function setOrderStateByModule($module_name)
    {
        Tools::displayAsDeprecated();
        $id_order_state = Db::getInstance()->getValue('
        SELECT `id_order_state`
        FROM `' . _DB_PREFIX_ . 'order_state`
        WHERE `module_name` = \'' . $module_name . '\'');

        // returns false if there is no state
        if (!$id_order_state) {
            return 1;
        }
        return $id_order_state;
    }

    public function linkShippingCost(Order $order)
    {
        $cart = new Cart($order->id_cart);
        $currency = new Currency($order->id_currency);
        $carrierId = $order->id_carrier;
        $id_address_delivery = $order->id_address_delivery;
        $order->total_paid_real = 0;
        $computingPrecision = $currency->precision;
        $order->product_list = array_values($cart->getProducts());
        $carrier = new Carrier($carrierId);
        $address = new Address($id_address_delivery);
        $default_country = new Country($address->id_country);

        $order->total_products = Tools::ps_round(
            (float) $cart->getOrderTotal(false, Cart::ONLY_PRODUCTS, $order->product_list, $carrierId),
            $computingPrecision
        );
        $order->total_products_wt = Tools::ps_round(
            (float) $cart->getOrderTotal(true, Cart::ONLY_PRODUCTS, $order->product_list, $carrierId),
            $computingPrecision
        );
        $order->total_discounts_tax_excl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(false, Cart::ONLY_DISCOUNTS, $order->product_list, $carrierId)),
            $computingPrecision
        );
        $order->total_discounts_tax_incl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(true, Cart::ONLY_DISCOUNTS, $order->product_list, $carrierId)),
            $computingPrecision
        );
        $order->total_discounts = $order->total_discounts_tax_incl;

        $order->total_shipping_tax_excl = Tools::ps_round(
            (float) $cart->getPackageShippingCost($carrierId, false, $default_country, $order->product_list, $default_country->id_zone),
            $computingPrecision
        );
        $order->total_shipping_tax_incl = Tools::ps_round(
            (float) $cart->getPackageShippingCost($carrierId, true, $default_country, $order->product_list, $default_country->id_zone),
            $computingPrecision
        );
        $order->total_shipping = $order->total_shipping_tax_incl;

        if (null !== $carrier && Validate::isLoadedObject($carrier)) {
            $order->carrier_tax_rate = $carrier->getTaxesRate(new Address((int) $cart->{Configuration::get('PS_TAX_ADDRESS_TYPE')}));
        }

        $order->total_wrapping_tax_excl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(false, Cart::ONLY_WRAPPING, $order->product_list, $carrierId)),
            $computingPrecision
        );
        $order->total_wrapping_tax_incl = Tools::ps_round(
            (float) abs($cart->getOrderTotal(true, Cart::ONLY_WRAPPING, $order->product_list, $carrierId)),
            $computingPrecision
        );
        $order->total_wrapping = $order->total_wrapping_tax_incl;

        $order->total_paid_tax_excl = Tools::ps_round(
            (float) $cart->getOrderTotal(false, Cart::BOTH, $order->product_list, $carrierId),
            $computingPrecision
        );
        $order->total_paid_tax_incl = Tools::ps_round(
            (float) $cart->getOrderTotal(true, Cart::BOTH, $order->product_list, $carrierId),
            $computingPrecision
        );
        $order->total_paid = $order->total_paid_tax_incl;
        $order->round_mode = Configuration::get('PS_PRICE_ROUND_MODE');
        $order->round_type = Configuration::get('PS_ROUND_TYPE');
        return $order;
    }

    public function paypalOrder(Order $order, Cart $cart, $transaction)
    {
        $paypalModule = Module::getInstanceByName('paypal');
        if (!Validate::isLoadedObject($paypalModule)) {
            return;
        }
        try {
            //if there isn't a method, then we don't create PaypalOrder and PaypalCapture
            $paypal_order = new PaypalOrder();
            $paypal_order->id_order = $order->id;
            $paypal_order->id_cart = $cart->id;
            $paypal_order->id_transaction = isset($transaction['id_transaction']) ? $transaction['id_transaction'] : '';
            $paypal_order->id_payment = isset($transaction['id_payment']) ? $transaction['id_payment'] : '';
            $paypal_order->payment_method = $transaction['payment_method'];
            $paypal_order->currency = $transaction['currency'];
            $paypal_order->total_paid = $transaction['total_paid'];
            $paypal_order->payment_status =  $transaction['payment_status'];
            $paypal_order->total_prestashop = $order->total_paid;
            $paypal_order->method = isset($transaction['method']) ? $transaction['method'] : '';
            $paypal_order->payment_tool = isset($transaction['payment_tool']) ? $transaction['payment_tool'] : 'PayPal';
            $paypal_order->sandbox = (int) Configuration::get('PAYPAL_SANDBOX');
            $paypal_order->intent = '';
            $paypal_order->save();
            $order_payment = new OrderPayment();
            $order_payment->order_reference = $order->reference;
            $order_payment->id_currency = $order->id_currency;
            $order_payment->amount = $transaction['total_paid'];
            $order_payment->payment_method = (int) Configuration::get('PAYPAL_SANDBOX') == 1 ? 'PayPal - SANDBOX' : 'PayPal';
            $order_payment->conversion_rate = $order->conversion_rate;
            $order_payment->transaction_id = isset($transaction['id_transaction']) ? $transaction['id_transaction'] : '';
            $order_payment->date_add = date("Y-m-d H:i:s");
            $order_payment->add();
            Db::getInstance()->insert(
                'paypal_processlogger',
                array(
                    'id_order' => $order->id,
                    'id_cart' => $cart->id,
                    'id_shop' => $order->id_shop,
                    'id_transaction' => '',
                    'log' => $transaction['payment_status'],
                    'status' => $transaction['payment_status'],
                    'sandbox' => (int) Configuration::get('PAYPAL_SANDBOX'),
                    'tools' => 'PayPal',
                    'date_add' => date("Y-m-d H:i:s"),
                    'date_transaction' => date("Y-m-d H:i:s"),
                )
            );
        } catch (Exception $e) {
        }
    }

    public function getCurrencyID($currency)
    {
        Tools::displayAsDeprecated();
        $id_currency = Db::getInstance()->getValue('
        SELECT `id_currency`
        FROM `' . _DB_PREFIX_ . 'currency`
        WHERE `iso_code` = \'' . $currency . '\'');

        // returns false if there is no state
        if (!$id_currency) {
            return 1;
        }
        return $id_currency;
    }

    function insertOrder(Order $order, $products, $note, $transaction)
    {
        // create a cart
        $cart = new Cart();
        $cart->id_address_delivery = $order->id_address_delivery;
        $cart->id_address_invoice = $order->id_address_invoice;
        $cart->id_carrier = $order->id_carrier;
        $cart->id_shop = Context::getContext()->shop->id;
        $cart->id_shop_group = Context::getContext()->shop->id_shop_group;
        $cart->id_customer = $order->id_customer;
        $cart->id_lang = Configuration::get('PS_LANG_DEFAULT');
        $customer = new Customer($order->id_customer);
        $cart->secure_key = $customer->secure_key;
        $cart->id_currency = 1;
        $deliveryOption = [$cart->id_address_delivery => $order->id_carrier . ','];
        $cart->setDeliveryOption($deliveryOption);
        $cart->gift_message = '';
        $cart->gift = 0;
        $cart->mobile_theme = 0;
        $cart->add();
        $cart->setWsCartRows($products);
        // create a order
        $order->id_cart = $cart->id;
        $order->id_lang = Configuration::get('PS_LANG_DEFAULT');
        $order->conversion_rate = 1;
        $order->invoice_number = 0;
        $order->delivery_number = 0;
        $order->invoice_date = '0000-00-00 00:00:00';
        $order->delivery_date = '0000-00-00 00:00:00';
        $order->shipping_number = '';
        $order->valid = 0;
        $order->mobile_theme = 0;
        $order->reference = Order::generateReference();
        $order->id_shop = Context::getContext()->shop->id;
        $order->id_shop_group = Context::getContext()->shop->id_shop_group;
        $order->gift_message = $cart->gift_message;
        $order->total_paid = $order->total_products;
        //load payment
        $payment = Module::getInstanceByName($order->payment);
        $order->payment = $payment->displayName;
        $order->module = $payment->name;
        //update total shipping
        $order = $this->linkShippingCost($order);
        $order->current_state = $this->setOrderStateByModule($payment->name);
        $order->secure_key = $customer->secure_key;
        $order->add();
        //insert order detail
        $orderDetail = new OrderDetail();
        $orderDetail->createList($order, $cart, 0, array_values($cart->getProducts()));
        //create order history
        $history = new OrderHistory();
        $history->id_order = $order->id;
        $history->id_order_state = $order->current_state;
        $history->id_employee = 0;
        $history->addWithemail();
        //add paypal payment
        if (isset($transaction)) {
            $this->paypalOrder($order, $cart, array(
                'payment_method' => $transaction->payment_method,
                'currency' => $transaction->currency,
                'total_paid' => $transaction->total_paid,
                'payment_status' => $transaction->payment_status,
                'id_transaction' => $transaction->id_transaction,
                'id_payment' => $transaction->id_payment,
                'method' => $transaction->method,
                'payment_tool' => $transaction->payment_tool
            ));
        }

        return $order;
    }

    function getProductTitleDesription($id_product_attribute, $id_lang)
    {
        $product_attributes = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT DISTINCT pa.`id_product_attribute` as id, agl.`name` as label,  al.`name` as value
            FROM `' . _DB_PREFIX_ . 'product_attribute` pa
            LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON pac.`id_product_attribute` = pa.`id_product_attribute`
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute` a ON a.`id_attribute` = pac.`id_attribute`
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_lang` al ON al.`id_attribute` = a.`id_attribute` AND al.`id_lang` = ' . $id_lang .'
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` agl ON agl.`id_attribute_group` = a.`id_attribute_group` AND agl.`id_lang` = ' . $id_lang .'
            WHERE pa.`id_product_attribute` =  ' . $id_product_attribute . '
            '
        );
        if (empty($product_attributes)) return "";
        $title = "";
        foreach ($product_attributes as $product_attribute) {
            if (empty($title)) {
                $title = $product_attribute['label'] . ": " . $product_attribute['value'];
            } else {
                $title = $title . " - " . $product_attribute['label'] . ": " . $product_attribute['value'];
            }
        }
        return $title;
    }

    public function manage()

    {
        $objects_products = array();
        $objects_products['empty'] = new Order();

        try {
            $order = new Order();
            $putresource = fopen('php://input', 'rb');
            $body = null;
            while ($putData = fread($putresource, 1024)) {
                $body .= $putData;
            }
            fclose($putresource);
            $json = json_decode($body);
            $note = null;
            $matches = null;
            $quantity = null;
            $attribute = null;

            if (isset($json)) {
                $order->id_carrier = $json->id_carrier;
                $order->id_customer = $json->id_customer;
                $order->id_currency = $this->getCurrencyID($json->id_currency ?? 'USD');
                $order->id_address_delivery = $json->id_address_delivery;
                $order->id_address_invoice = $json->id_address_invoice;
                $order->payment = $json->module;
                /*
                more detail
                */
                $note = $json->notes ?? null;
                $matches = $json->products;
                $quantity = $json->quantity;
                $attribute = $json->attribute;
            } else {
                $order->id_carrier = $this->wsObject->urlFragments['id_carrier'];
                $order->id_customer = $this->wsObject->urlFragments['id_customer'];
                $order->id_currency = $this->getCurrencyID($this->wsObject->urlFragments['id_currency'] ?? 'USD');
                $order->id_address_delivery = $this->wsObject->urlFragments['id_address_delivery'];
                $order->id_address_invoice = $this->wsObject->urlFragments['id_address_invoice'];
                $order->payment = $this->wsObject->urlFragments['module'];
                /*
                more detail
                */
                $note = $this->wsObject->urlFragments['notes'] ?? null;
                $matches = json_decode($this->wsObject->urlFragments['products']);
                $quantity = json_decode($this->wsObject->urlFragments['quantity']);
                $attribute = json_decode($this->wsObject->urlFragments['attribute']);
            }

            $products = array();
            if (is_array($matches)) {
                for ($i = 0; $i < count($matches); $i++) {
                    $product_value['id_product'] = $matches[$i];
                    $product_value['id_product_attribute'] = strval($attribute[$i]) != '-1' ? (int)$attribute[$i] : 0;
                    $product_value['id_address_delivery'] = $order->id_address_delivery;
                    $product_value['quantity'] = $quantity[$i];
                    $products[] = $product_value;
                }
            }
            /// get guest id
            if ($order->id_customer == null) {
                $address = new Address($order->id_address_delivery);
                $order->id_customer = $address->id_customer;
            }
            if ($order->id_customer != null) {
                $objects_products[] = $this->insertOrder($order, $products, $note, isset($json) ? ($json->transaction ?? null) : null);
            } else {
                $this->wsObject->setError(404, 'Opps, we can not get your id', 'auth/user');
            }
        } catch (Exception $e) {
        }
        $this->_resourceConfiguration = $objects_products['empty']->getWebserviceParameters();
        // $this->_resourceConfiguration = $products['empty']->getWebserviceParameters();
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_products, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
