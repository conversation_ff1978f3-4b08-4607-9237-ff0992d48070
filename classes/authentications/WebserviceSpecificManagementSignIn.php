<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/authentications/FluxCustomer.php';

class WebserviceSpecificManagementSignIn implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    function createSocialAccount($email, $name, $firstName, $lastName, $userName)
    {
        $email_exists = FluxCustomer::customerExists($email);
        if ($email_exists) {
            $user = new FluxCustomer();
            try {
                $user->getByEmail($email);
            } catch (Exception $e) {
                $user = null;
            }
            return $user;
        } else {
            $user = new FluxCustomer();
            $user->email = $email;
            $user->firstname = preg_replace('/\d/', '', $firstName);
            $user->lastname = preg_replace('/\d/', '', $lastName);
            $user->setWsPasswd(`$name@#1`);
            $user->id = null;
            $user->add();
            return $user;
        }
    }

    public function fb_connect($access_token)
    {
        $fields = 'id,name,first_name,last_name,email';
        $enable_ssl = true;
        if (!isset($access_token)) {
            $this->wsObject->setError(400, "You must include a 'access_token' variable. Get the valid access_token for this app from Facebook API.", "invalid_login");
            return null;
        }
        $url = 'https://graph.facebook.com/me/?fields=' . $fields . '&access_token=' . $access_token;

        $result = Tools::file_get_contents($url);

        $result = json_decode($result);

        if (isset($result->email)) {
            $user_name = strtolower($result->first_name . '.' . $result->last_name);
            return $this->createSocialAccount($result->email, $result->name, $result->first_name, $result->last_name, $user_name);
        } else {
            $this->wsObject->setError(400, "Your 'access_token' did not return email of the user. Without 'email' user can't be logged in or registered. Get user email extended permission while joining the Facebook app.", "invalid_login");
            return null;
        }
    }

    public function google_login($access_token)
    {
        if (!isset($access_token)) {
            $this->wsObject->setError(400, "You must include a 'access_token' variable. Get the valid access_token for this app from Facebook API.", "invalid_login");
            return null;
        }
        $url = 'https://www.googleapis.com/oauth2/v1/userinfo?alt=json&access_token=' . $access_token;
        $result = Tools::file_get_contents($url);
        $result = json_decode($result);
        if (isset($result->email)) {
            $firstName = $result->given_name;
            $lastName = $result->family_name;
            $email = $result->email;
            $display_name = $firstName . " " . $lastName;
            $user_name = $firstName . "." . $lastName;
            return $this->createSocialAccount($email, $display_name, $firstName, $lastName, $user_name);
        } else {
            $this->wsObject->setError(400, "Your 'token' did not return email of the user. Without 'email' user can't be logged in or registered. Get user email extended permission while joining the Google app.", "invalid_login");
            return null;
        }
    }

    public function firebase_sms_login_v2($phone)
    {
        if (!isset($phone)) {
            $this->wsObject->setError(400, "You must include a 'phone' variable.", "invalid_login");
            return null;
        }

        if (isset($phone)) {
            $domain = $_SERVER['SERVER_NAME'];
            if (count(explode(".", $domain)) == 1) {
                $domain = "flutter.io";
            }
            $user_name = $phone;
            $user_email = $phone . "@" . $domain;
            return $this->createSocialAccount($user_email, $user_name, "Phone", "Number", $user_name);
        }
        $this->wsObject->setError(400, "Unknow Error", "invalid_login");
        return null;
    }

    function jwtDecode($token)
    {
        $splitToken = explode(".", $token);
        $payloadBase64 = $splitToken[1]; // Payload is always the index 1
        $decodedPayload = json_decode(urldecode(base64_decode($payloadBase64)), true);
        return $decodedPayload;
    }

    public function apple_login($token)
    {
        $decoded = $this->jwtDecode($token);
        $user_email = $decoded["email"];
        if (!isset($user_email)) {
            $this->wsObject->setError(400, "Can't get the email to create account.", "invalid_login");
            return;
        }
        $display_name = explode("@", $user_email)[0];
        $user_name = $display_name;

        return $this->createSocialAccount($user_email, $display_name, $display_name, $display_name, $user_name);
    }


    public function manage()
    {

        $objects = array();
        $objects['empty'] = new FluxCustomer();
        $putresource = fopen('php://input', 'rb');
        $body = null;
        while ($putData = fread($putresource, 1024)) {
            $body .= $putData;
        }
        fclose($putresource);
        $json = json_decode($body);
        $email = $json->email ?? null;
        $passwd = $json->passwd ?? null;
        $access_token = $json->access_token ?? null;
        $isFacebook = $json->isFacebook ?? false;
        $isGoogle = $json->isGoogle ?? false;
        $isApple = $json->isApple ?? false;
        $isSMS = $json->isSMS ?? false;
        $phone = $json->phone ?? null;
        $user = null;
        if ($email != null && $passwd != null) {
            $customer = new FluxCustomer();
            try {
                $customer->getByEmail($email, $passwd);
            } catch (Exception $e) {
                $customer = null;
            }
            if ($customer != null && $customer->id != null) {
                $user = $customer;
            } else {
                $this->wsObject->setError(400, "Invalid username/email and/or password.", "invalid_login");
            }
        } else if ($isFacebook) {
            $user = $this->fb_connect($access_token);
        } else if ($isGoogle) {
            $user = $this->google_login($access_token);
        } else if ($isSMS) {
            $user = $this->firebase_sms_login_v2($phone);
        } else if ($isApple) {
            $user = $this->apple_login($access_token);
        }
        if ($user != null && $user->id != null) {
            // $session = new CustomerSession();
            // $session->setUserId((int) $user->id);
            // $session->setToken(sha1(time() . uniqid()));
            // $session->add();
            // $user->token = $session->token;
            $objects[] = $user;
        }
        

        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}