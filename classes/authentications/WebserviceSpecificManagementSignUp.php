<?php

class WebserviceSpecificManagementSignUp implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    function getModules()
    {
        $hook_payment = 'Payment';
        if (Db::getInstance()->getValue('SELECT `id_hook` FROM `' . _DB_PREFIX_ . 'hook` WHERE `name` = \'paymentOptions\'')) {
            $hook_payment = 'paymentOptions';
        }

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            'SELECT DISTINCT m.`id_module`, h.`id_hook`, m.`name`, hm.`position`
        FROM `' . _DB_PREFIX_ . 'module` m
        LEFT JOIN `' . _DB_PREFIX_ . 'hook_module` hm ON hm.`id_module` = m.`id_module`
        LEFT JOIN `' . _DB_PREFIX_ . 'hook` h ON hm.`id_hook` = h.`id_hook`
        WHERE h.`name` = \'' . pSQL($hook_payment) . '\'
        GROUP BY hm.id_hook, hm.id_module
        ORDER BY hm.`position`, m.`name` DESC'
        );
    }

    function getModuleDisplayName($module)
    {
        // Config file
        $config_file = _PS_MODULE_DIR_ . $module . '/config.xml';
        // For "en" iso code, we keep the default config.xml name
        if (!file_exists($config_file)) {
            return 'Module ' . ucfirst($module);
        }

        // Load config.xml
        libxml_use_internal_errors(true);
        $xml_module = @simplexml_load_file($config_file);
        if (!$xml_module) {
            return 'Module ' . ucfirst($module);
        }

        // Return Display Name
        return Module::configXmlStringFormat($xml_module->displayName);
    }

    public function manage()

    {

        $objects_users = array();
        $objects_users['empty'] = new Customer();
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $putresource = fopen('php://input', 'rb');
            $body = null;
            while ($putData = fread($putresource, 1024)) {
                $body .= $putData;
            }
            fclose($putresource);
            $json = json_decode($body);
            $email = $json->email ?? null;
            $id_gender = $json->id_gender ?? 1;
            $firstname = $json->firstname ?? '';
            $lastname = $json->lastname ?? '';
            $passwd = $json->passwd ?? null;
            if ($email == null || $passwd == null) {
                $this->wsObject->setError(400, 'The request is missing information', 'missing');
            } else {
                $user = new Customer();
                $user->email = $email;
                $user->id_gender = $id_gender;
                $user->firstname = $firstname;
                $user->lastname = $lastname;
                $user->setWsPasswd($passwd);
                $users = Customer::getCustomersByEmail($email);
                if (empty($users)) {
                    $user->add();
                    $objects_users[] = $user;
                } else {
                    $this->wsObject->setError(400, 'The email address is already exits', 'user/exits');
                }
            }
        } else {
            $this->wsObject->setError(400, 'The method is not found', 'method/not-found');
        }

        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_users, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
