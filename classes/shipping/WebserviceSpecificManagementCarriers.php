<?php
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';

class ProductShippingItem
{
    public $id;
    public $price;
    public $quantity;
    public $is_virtual;
    public $additional_shipping_cost;
    public $weight;
    public $id_supplier;
    public $height;
    public $depth;
    public $width;


    public function __construct($id = null, $price = null, $quantity = null, $is_virtual = null, $additional_shipping_cost = null, $weight = null, $id_supplier = null, $height = null, $depth = null, $width = null)
    {
        $this->id = $id;
        $this->price = $price;
        $this->quantity = $quantity;
        $this->is_virtual = $is_virtual;
        $this->additional_shipping_cost = $additional_shipping_cost;
        $this->weight = $weight;
        $this->id_supplier = $id_supplier;
        $this->height = $height;
        $this->depth = $depth;
        $this->width = $width;
    }
}

class WebserviceSpecificManagementCarriers implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;

    /** @var WebserviceRequest */

    protected $wsObject;

    public function getWsObject()
    {
        return $this->wsObject;
    }



    public function getObjectOutput()
    {
        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()
    {
        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)
    {
        $this->wsObject = $obj;
        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)
    {
        $this->objOutput = $obj;
        return $this;
    }

    function getTotalPriceProducts($products)
    {
        $price = 0;
        foreach ($products as $product) {
            $price = $price + $product->price * $product->quantity;
        }
        return $price;
    }

    function getTotalWeightProducts($products)
    {
        $weight = 0;
        foreach ($products as $product) {
            $weight = $weight + $product->weight * $product->quantity;
        }
        return $weight;
    }

    function getTotalHeightProducts($products)
    {
        $height = 0;
        foreach ($products as $product) {
            $height = $height + $product->height * $product->quantity;
        }
        return $height;
    }

    function getTotalWidthProducts($products)
    {
        $width = 0;
        foreach ($products as $product) {
            $width = $width + $product->width * $product->quantity;
        }
        return $width;
    }

    function getTotalDepthProducts($products)
    {
        $depth = 0;
        foreach ($products as $product) {
            $depth = $depth + $product->depth * $product->quantity;
        }
        return $depth;
    }

    function getTotalProducts($products)
    {
        $price = 0;
        foreach ($products as $product) {
            $price = $price + $product->price * $product->quantity;
        }
        return $price;
    }

    function getPackageShippingCost($id_carrier = null, $id_zone = null, $product_list = null, $use_tax = true)
    {
        $carrier = new Carrier($id_carrier);

        // Start with shipping cost at 0
        $shipping_cost = 0;


        $shipping_method = $carrier->getShippingMethod();

        // Get shipping cost using correct method
        if ($carrier->range_behavior) {

            if (($shipping_method == Carrier::SHIPPING_METHOD_WEIGHT && !Carrier::checkDeliveryPriceByWeight($carrier->id, $this->getTotalWeightProducts($product_list), (int) $id_zone))
                || (
                    $shipping_method == Carrier::SHIPPING_METHOD_PRICE && !Carrier::checkDeliveryPriceByPrice($carrier->id, $this->getTotalProducts($product_list), $id_zone)
                )
            ) {
                $shipping_cost += 0;
            } else {
                if ($shipping_method == Carrier::SHIPPING_METHOD_WEIGHT) {
                    $shipping_cost += $carrier->getDeliveryPriceByWeight($this->getTotalWeightProducts($product_list), $id_zone);
                } else { // by price
                    $shipping_cost += $carrier->getDeliveryPriceByPrice($this->getTotalProducts($product_list), $id_zone);
                }
            }
        } else {
            if ($shipping_method == Carrier::SHIPPING_METHOD_WEIGHT) {
                $shipping_cost += $carrier->getDeliveryPriceByWeight($this->getTotalWeightProducts($product_list), $id_zone);
            } else {
                $shipping_cost += $carrier->getDeliveryPriceByPrice($this->getTotalProducts($product_list), $id_zone);
            }
        }

        //handling charges
        $configuration = Configuration::getMultiple(array(
            'PS_SHIPPING_FREE_PRICE',
            'PS_SHIPPING_HANDLING',
            'PS_SHIPPING_METHOD',
            'PS_SHIPPING_FREE_WEIGHT',
        ));

        if (isset($configuration['PS_SHIPPING_HANDLING']) && $carrier->shipping_handling) {
            $shipping_cost += (float) $configuration['PS_SHIPPING_HANDLING'];
        }

        // Additional Shipping Cost per product
        foreach ($product_list as $product) {
            if (!$product->is_virtual) {
                $shipping_cost += $product->additional_shipping_cost * $product->quantity;
            }
        }

        // $shipping_cost = Tools::convertPrice($shipping_cost, Currency::getCurrencyInstance((int) $this->id_currency));

        //get external shipping cost from module
        //$shipping_cost = $this->getPackageShippingCostFromModule($carrier, $shipping_cost, $products);

        // if (Configuration::get('PS_ATCP_SHIPWRAP')) {
        //     if (!$use_tax) {
        //         // With PS_ATCP_SHIPWRAP, we deduce the pre-tax price from the post-tax
        //         // price. This is on purpose and required in Germany.
        //         $shipping_cost /= (1 + $this->getAverageProductsTaxRate());
        //     }
        // } else {
        //     // Apply tax
        //     if ($use_tax && isset($carrier_tax)) {
        //         $shipping_cost *= 1 + ($carrier_tax / 100);
        //     }
        // }

        $shipping_cost = (float) Tools::ps_round((float) $shipping_cost, 2);

        return $shipping_cost;
    }


    public function manage()

    {
        $objects_carriers = array();
        $objects_carriers['empty'] = new Carrier();
        $putresource = fopen('php://input', 'rb');
        $body = null;
        while ($putData = fread($putresource, 1024)) {
            $body .= $putData;
        }
        fclose($putresource);
        $params = $this->wsObject->urlFragments;
        $json = json_decode($body);
        $line_items = $json->line_items ?? [];
        $_country = $json->country ?? null;
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        $carriers = Carrier::getCarriers($id_lang, true, false);
        $products = array();
        for ($i = 0; $i < count($line_items); $i++) {
            $item = $line_items[$i];
            $attribute = $item->variation_id ?? null;
            $_product = new Product($item->product_id);
            $product = new ProductShippingItem($_product->id, $_product->price, 0, $_product->is_virtual, $_product->additional_shipping_cost, $_product->weight, $_product->id_supplier, $_product->height, $_product->depth, $_product->width);
            $product->quantity = $item->quantity;
            if (Configuration::get('CUSTOMAPI_ENABLE_TAX', true) == true && empty($product->id_supplier)) {
                $product->price = number_format((float) (Product::getPriceStatic($product->id, true, null, 2, null, false, true, $item->quantity)), 2, '.', '');
            }
            if ($attribute != null) {
                $combination = new Combination($attribute);
                if (Configuration::get('CUSTOMAPI_ENABLE_TAX', true) == true) {
                    $product->price = number_format((float) (Product::getPriceStatic($product->id, true, $attribute[$i], 2, null, false, true, $item->quantity)), 2, '.', '');
                } else {
                    $product->price = number_format((float) ($product->price + $combination->price), 2, '.', '');
                }
                $product->weight = $product->weight + $combination->weight;
            }
            $products[] = $product;
        }
        $total_price = $this->getTotalPriceProducts($products);
        $total_weight = $this->getTotalWeightProducts($products);
        $total_width = $this->getTotalWidthProducts($products);
        $total_depth = $this->getTotalDepthProducts($products);
        $total_height = $this->getTotalHeightProducts($products);
        foreach ($carriers as $carrier) {
            $item = new Carrier($carrier['id_carrier']);
            if ($item->max_weight > 0 && $item->max_weight < $total_weight) {
                continue;
            }
            if ($item->max_width > 0 && $item->max_width < $total_width) {
                continue;
            }
            if ($item->max_depth > 0 && $item->max_depth < $total_depth) {
                continue;
            }
            if ($item->max_height > 0 && $item->max_height < $total_height) {
                continue;
            }
            if ($_country != null) {
                $id_country = Country::getByIso($_country);
                $country = new Country($id_country);
                $item->shipping_external = $this->getPackageShippingCost($item->id, $country->id_zone, $products);
                $free_shipping_price = Configuration::get('PS_SHIPPING_FREE_PRICE');
                if ($total_price >= $free_shipping_price && $free_shipping_price > 0.0) {
                    $item->shipping_external = 0.00;
                }
                $free_shipping_weight = Configuration::get('PS_SHIPPING_FREE_WEIGHT');
                if ($total_weight >= $free_shipping_weight && $free_shipping_weight > 0.0) {
                    $item->shipping_external = 0.00;
                }
            }
            $item->delay = FluxUntils::getName($item->delay, $id_lang);
            $objects_carriers[] = $item;
        }

        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_carriers, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
