<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxCombination.php';

/**
 * This class is deprecated but is still present to support legacy APIs for
 * FluxStore 3.16.0 and below.
 */
class WebserviceSpecificManagementAttribute implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;



    public function setUrlSegment($segments)

    {

        $this->urlSegment = $segments;

        return $this;
    }



    public function getUrlSegment()

    {

        return $this->urlSegment;
    }

    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function getAttributes($id_product)
    {
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_product_attribute`
            FROM `' . _DB_PREFIX_ . 'product_attribute`
            WHERE `id_product` = ' . $id_product . '
            '
        );
    }

    public function getStockQuantity($id_product_attribute)
    {
        $shop_id = Configuration::get('CUSTOMAPI_SHOP_ID', null);

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT SUM(quantity) as Total 
            FROM `' . _DB_PREFIX_ . 'stock_available`
            WHERE `id_product_attribute` = \'' . $id_product_attribute . '\'
            '
        );
        if (empty($result)) return 0;
        return $result[0]['Total'];
    }

    public function getTaxStockQuantity($id_product, $id_product_attribute)
    {
        $query = new DbQuery();
        $query->select('SUM(quantity)');
        $query->from('stock_available');

        // if null, it's a product without attributes
        if ($id_product !== null) {
            $query->where('id_product = ' . (int) $id_product);
        }

        $query->where('id_product_attribute = ' . (int) $id_product_attribute);
        $query = StockAvailable::addSqlShopRestriction($query, 1);
        $result = (int) Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($query);
        return $result;
    }

    public function isExistIdShop($id_shop_default)
    {
        if (empty($id_shop_default)) return false;
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_shop`
            FROM `' . _DB_PREFIX_ . 'shop`
            WHERE `id_shop` = ' . $id_shop_default . '
            LIMIT 1'
        );
        if (empty($result)) return false;
        return true;
    }

    public function getSalePrice($product, $combination)
    {
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_specific_price`, `reduction`, `reduction_type`
            FROM `' . _DB_PREFIX_ . 'specific_price`
            WHERE `id_product` = ' . $product->id . '
            LIMIT 1'
        );
        if (empty($result)) return $combination->price;
        if ($result[0]['reduction_type'] != "percentage") return $combination->price - $result[0]['reduction'];
        return ($combination->price) * (1 - $result[0]['reduction']);
    }

    public function getLanguageId($lang)
    {
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_lang`
            FROM `' . _DB_PREFIX_ . 'lang`
            WHERE `iso_code` = \'' . $lang . '\'
            LIMIT 1'
        );
    }


    public function manage()

    {

        $objects_products = array();

        $objects_products['empty'] = new FluxCombination();

        $id_product = $this->wsObject->urlFragments['id_product'];

        $id_shop_default = $this->wsObject->urlFragments['id_shop_default'] ?? null;

        $combinations = $this->getAttributes($id_product);
        $lang = $this->wsObject->urlFragments['lang'] ?? 'en';
        $lang = $this->getLanguageId($lang) ?? [];
        $lang = count($lang) > 0 ? strval($lang[0]['id_lang']) : null;

        $product = new Product($id_product);
        if (empty($product->price)) {
            $product->price = 0.0;
        }

        foreach ($combinations as $combination) {
            $combination = new FluxCombination($combination['id_product_attribute']);
            $combination->lang = $lang;
            if ($combination->id_product != $product->id) continue;
            if (!Configuration::get('PS_STOCK_MANAGEMENT') || Configuration::get('PS_ORDER_OUT_OF_STOCK')) {
                $combination->quantity = Configuration::get('CUSTOMAPI_ORDER_STOCK', '99');
            } else {
                $combination->quantity = $this->getStockQuantity($combination->id) ?? '0';
            }
            if (Configuration::get('CUSTOMAPI_ENABLE_TAX', true) == true) {
                $combination->price = number_format((float) (Product::getPriceStatic($combination->id_product, true, $combination->id, 2, null, false, false)), 2, '.', '');
            } else {
                $combination->price = number_format((float) ($product->price + $combination->price), 2, '.', '');
            }
            if (Configuration::get('CUSTOMAPI_ENABLE_TAX', true) == true) {
                $combination->wholesale_price = number_format((float) (Product::getPriceStatic($combination->id_product, true, $combination->id, 2)), 2, '.', '');
            } else {
                $combination->wholesale_price = number_format((float) ($this->getSalePrice($product, $combination)), 2, '.', '');
            }
            $objects_products[] = $combination;
        }
        $this->_resourceConfiguration = $objects_products['empty']->getWebserviceParameters();

        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_products, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
