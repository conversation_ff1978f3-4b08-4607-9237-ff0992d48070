<?php

use PayPal\Api\Payment;
use PayPal\Auth\OAuthTokenCredential;
use PayPal\Rest\ApiContext;
use PaypalAddons\classes\AbstractMethodPaypal;
use PaypalAddons\services\StatusMapping;

include_once _PS_MODULE_DIR_ . 'customapi/classes/orders/FluxOrder.php';


class FluxPayPal
{
    /**
     * Return an instance of the specified module.
     *
     * @param string $module_name Module name
     *
     * @return PayPal|false
     */
    static public function getModule()
    {
        $paypal = Module::getInstanceByName('paypal');
        return $paypal;
    }

    static public function getAmount($transaction)
    {
        $amount = 0;
        foreach ($transaction->getRelatedResources() as $resources) {
            $resource = null;
            if (isset($resources->sale)) {
                $resource = $resources->sale;
            }
            if (isset($resources->refund)) {
                $resource = $resources->refund;
            }
            if ($resource === null) {
                continue;
            }
            if (is_callable([$resource, 'amount'], true)) {
                $transactionAmount = $resource->amount;
                if (is_callable([$transactionAmount, 'total'], true)) {
                    $amount += $transactionAmount->total;
                }
            }
        }
        return $amount;
    }

    static public function getCurrency($transaction)
    {
        foreach ($transaction->getRelatedResources() as $resources) {
            $resource = null;
            if (isset($resources->sale)) {
                $resource = $resources->sale;
            }
            if (isset($resources->refund)) {
                $resource = $resources->refund;
            }
            if ($resource === null) {
                continue;
            }
            if (is_callable([$resource, 'amount'], true)) {
                $transactionAmount = $resource->amount;
                if (is_callable([$transactionAmount, 'currency'], true)) {
                    return $transactionAmount->currency;
                }
            }
        }
    }

    static public function completeOrder(Order $order, Cart $cart, $id_payment)
    {
        try {
            if (empty($id_payment)) return;
            $paypal = FluxPayPal::getModule();
            if (!Validate::isLoadedObject($paypal)) {
                return;
            }
            $method = AbstractMethodPaypal::load();
            $payment_method = 'EC';
            if (!$method->isConfigured()) {
                $payment_method = 'PPP';
                $method = AbstractMethodPaypal::load('PPP');
            }
            if (!$method->isConfigured()) {
                $payment_method = 'EC';
                $method = AbstractMethodPaypal::load('EC');
            }
            if (!$method->isConfigured()) {
                return;
            }
            $apiContext = new ApiContext(
                new OAuthTokenCredential(
                    $method->getClientId(),
                    $method->getSecret()
                )
            );
            $apiContext->setConfig(
                [
                    'mode' => $method->isSandbox() ? 'sandbox' : 'live',
                    'log.LogEnabled' => false,
                    'cache.enabled' => true,
                ]
            );
            $payment_name = $method->isSandbox() ? 'PayPal - SANDBOX' : 'PayPal';
            $order->payment = $payment_name;
            $payment = Payment::get($id_payment, $apiContext);
            $payer = $payment->getPayer();
            $transaction = $payment->getTransactions()[0];
            $paypal_order = new PaypalOrder();
            $paypal_order->id_order = $order->id;
            $paypal_order->id_cart = $cart->id;
            $paypal_order->id_transaction = '';
            $paypal_order->id_payment = $id_payment;
            $paypal_order->payment_method = $payer->getPaymentMethod();
            $paypal_order->currency = FluxPayPal::getCurrency($transaction) ?? Currency::getDefaultCurrency()->iso_code;
            $paypal_order->total_paid = FluxPayPal::getAmount($transaction);
            $paypal_order->payment_status = $payment->getState();
            $paypal_order->total_prestashop = $order->total_paid;
            $paypal_order->method = $payment_method;
            $paypal_order->payment_tool = '';
            $paypal_order->sandbox = $method->isSandbox();
            $paypal_order->intent = $payment->getIntent();
            $paypal_order->save();
            ////
            $order_payment = new OrderPayment();
            $order_payment->order_reference = $order->reference;
            $order_payment->id_currency = $order->id_currency;
            $order_payment->amount = FluxPayPal::getAmount($transaction);
            $order_payment->payment_method = $payment_name;
            $order_payment->conversion_rate = $order->conversion_rate;
            $order_payment->transaction_id = '';
            $order_payment->date_add = date("Y-m-d H:i:s");
            $order_payment->add();
            ////
            if ($payment->getState() == 'approved') {
                $status = new StatusMapping();
                $id_status = $status->getAcceptedStatus();
                FluxOrder::addOrderHistory($order->id, $id_status);
                $order->current_state = $id_status;
                $order->update();
            }
            if ($order_payment->id != null) {
                FluxOrder::addInvoice($order, $order_payment->id);
            }
            Db::getInstance()->insert(
                'paypal_processlogger',
                array(
                    'id_order' => $order->id,
                    'id_cart' => $cart->id,
                    'id_shop' => $order->id_shop,
                    'id_transaction' => '',
                    'log' => $payment->getState(),
                    'status' => $payment->getState(),
                    'sandbox' => (int) $method->isSandbox(),
                    'tools' => 'PayPal',
                    'date_add' => date("Y-m-d H:i:s"),
                    'date_transaction' => date("Y-m-d H:i:s"),
                )
            );
        } catch (Exception $e) {
        }
    }
}
