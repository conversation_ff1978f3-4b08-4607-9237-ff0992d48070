<?php

class PaymentMethod extends ObjectModel {
    public $id_hook;
    public $name;
    public $title;
    public $description;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'payment',
        'primary' => 'id_payment',
        'multilang' => true,
        'fields' => array(
            'id_hook' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'name' => ['type' => self::TYPE_STRING, 'validate' => 'isCleanHtml', 'required' => true, 'size' => 255],
            'title' => ['type' => self::TYPE_STRING, 'validate' => 'isCleanHtml', 'required' => true, 'size' => 255],
            'description' => ['type' => self::TYPE_STRING, 'validate' => 'isCleanHtml', 'required' => true, 'size' => 255],
        )
    );

    protected $webserviceParameters = array(
      'objectNodeName' => 'payment',
      'objectsNodeName' => 'payment_methods',
      'fields' => array(
          'id_hook' => array('required' => true),
          'name' => array('required' => true),
          'title' => array('required' => true),
          'description' => []
        )
    );
}