<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/payments/PaymentMethod.php';

class WebserviceSpecificManagementPaymentMethods implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function manage()
    {
        $objects_payments['empty'] = new PaymentMethod();

        $modules = PaymentModule::getInstalledPaymentModules();

        foreach($modules as $module) {
            $payment = new PaymentMethod();
            $payment->id = $module['id_module'];
            $payment->id_hook = $module['id_hook'];
            $payment->title = Module::getModuleName($module['name']);
            $payment->name = $module['name'];
            if ($module['name'] == 'ps_checkout') continue;
            try {
                $_module = Module::getInstanceByName($module['name']);
                $payment->title = $_module->displayName;
            } catch (Exception $e) {
            }
            $objects_payments[] = $payment;
        }
        
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_payments, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}