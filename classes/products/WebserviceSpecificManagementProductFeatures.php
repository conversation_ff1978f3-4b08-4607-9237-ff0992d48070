<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxFeature.php';
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';

class WebserviceSpecificManagementProductFeatures implements WebserviceSpecificManagementInterface
{
    /** @var WebserviceOutputBuilder */
    protected $objOutput;
    protected $output;

    /** @var WebserviceRequest */
    protected $wsObject;

    public function setUrlSegment($segments)
    {
        $this->urlSegment = $segments;
        return $this;
    }

    public function getUrlSegment()
    {
        return $this->urlSegment;
    }

    public function getWsObject()
    {
        return $this->wsObject;
    }

    public function getObjectOutput()
    {
        return $this->objOutput;
    }

    /**
     * This must be return a string with specific values as WebserviceRequest expects.
     *
     * @return string
     */
    public function getContent()
    {
        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }

    public function setWsObject(WebserviceRequestCore $obj)
    {
        $this->wsObject = $obj;
        return $this;
    }

    /**
     * @param WebserviceOutputBuilderCore $obj
     * @return WebserviceSpecificManagementInterface
     */
    public function setObjectOutput(WebserviceOutputBuilderCore $obj)
    {
        $this->objOutput = $obj;
        return $this;
    }

    public function getFeatures($id_feature = null)
    {
        $sql = '
            SELECT f.`id_feature`
            FROM `' . _DB_PREFIX_ . 'feature` f
            ' . ($id_feature ? 'WHERE f.`id_feature` = ' . (int)$id_feature : '') . '
            ORDER BY f.`position` ASC
        ';
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    public function getLanguageId($lang)
    {
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_lang`
            FROM `' . _DB_PREFIX_ . 'lang`
            WHERE `iso_code` = \'' . $lang . '\'
            LIMIT 1'
        );
    }

    /**
     * Check if a feature has related products based on categoryIds and brandIds
     *
     * @param int $id_feature Feature ID
     * @param string $categoryIds Comma-separated list of category IDs
     * @param string $brandIds Comma-separated list of brand/manufacturer IDs
     * @return bool True if feature has related products, false otherwise
     */
    public function hasRelatedProducts($id_feature, $categoryIds = null, $brandIds = null)
    {
        // If no filters are provided, just check if the feature has any products
        if (empty($categoryIds) && empty($brandIds)) {
            $query = '
            SELECT 1
            FROM `' . _DB_PREFIX_ . 'product` p
            JOIN `' . _DB_PREFIX_ . 'feature_product` fp ON fp.`id_product` = p.`id_product`
            WHERE p.`active` = 1
            AND fp.`id_feature` = ' . (int)$id_feature . '
            LIMIT 1';

            $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
            return !empty($result);
        }

        // Build query with filters
        $query = '
        SELECT 1
        FROM `' . _DB_PREFIX_ . 'product` p
        JOIN `' . _DB_PREFIX_ . 'feature_product` fp ON fp.`id_product` = p.`id_product`
        WHERE p.`active` = 1
        AND fp.`id_feature` = ' . (int)$id_feature;

        // Add category filter if provided
        if (!empty($categoryIds)) {
            $query .= '
            AND EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'category_product` cp
                WHERE cp.`id_product` = p.`id_product`
                AND cp.`id_category` IN (' . pSQL($categoryIds) . ')
            )';
        }

        // Add brand/manufacturer filter if provided
        if (!empty($brandIds)) {
            $query .= '
            AND p.`id_manufacturer` IN (' . pSQL($brandIds) . ')';
        }

        // Add LIMIT 1 for better performance since we only need to know if any products exist
        $query .= ' LIMIT 1';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        return !empty($result);
    }

    public function manage()
    {
        $objects_features = array();
        $objects_features['empty'] = new FluxFeature();

        $params = $this->wsObject->urlFragments;
        $id_feature = $params['id_feature'] ?? null;
        $lang = $params['lang'] ?? 'en';

        $categoryIds = $params['categoryIds'] ?? null;
        $brandIds = $params['brandIds'] ?? null;

        $lang = $this->getLanguageId($lang) ?? [];
        $id_lang = count($lang) > 0 ? strval($lang[0]['id_lang']) : null;

        $features = $this->getFeatures($id_feature);
        foreach ($features as $feature) {
            $id = $feature['id_feature'];
            $item = new FluxFeature($id, $id_lang, null, $categoryIds, $brandIds);
            $item->name = FluxUntils::getName($item->name, $id_lang);

            // Set is_visible based on whether there are related products
            $item->is_visible = $this->hasRelatedProducts($id, $categoryIds, $brandIds);

            $objects_features[] = $item;
        }

        $this->_resourceConfiguration = $objects_features['empty']->getWebserviceParameters();
        $this->wsObject->setFieldsToDisplay();
        $this->output .= $this->objOutput->getContent($objects_features, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
