<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxProduct.php';
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';

class WebserviceSpecificManagementProducts implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;

    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()
    {
        return $this->wsObject;
    }

    public function getObjectOutput()
    {
        return $this->objOutput;
    }

    public function getContent()
    {
        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)
    {
        $this->wsObject = $obj;
        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)
    {
        $this->objOutput = $obj;
        return $this;
    }

    public function getProducts($category, $page, $per_page, $search, $order, $order_by, $attributes, $features, $include, $on_sale, $min_price, $max_price, $product)
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($per_page < 0) {
            $per_page = 1;
        }
        $start = ($page - 1) * $per_page;
        $_order = strtolower($order ?? 'desc');
        if ($_order != 'desc' && $_order != 'asc') {
            $_order = 'desc';
        }
        $_order = strtoupper($_order);
        $_orderby = 'p.`date_add`';
        switch ($order_by) {
            case 'date':
                $_orderby = 'p.`date_add`';
                break;
            case 'title':
                $_orderby = 'pl.`name`';
                break;
            case 'price':
                if ($_order == 'ASC') {
                    $_orderby = 'price';
                } else {
                    $_orderby = 'p.`price`';
                }
                break;
            default:
                if ($order_by != null) {
                    $_orderby = 'p.`' . $order_by . '`';
                }
        }
        $enable_lang = !empty($search) || $order_by == 'title';
        $enable_specific_price = ($order_by == 'price' || !empty($min_price) || !empty($max_price) || $on_sale == true);
        $enable_having = !empty($min_price) || !empty($max_price);

        $query = '
        SELECT DISTINCT p.`id_product`
        ' . ($enable_specific_price ? ', (p.`price` - IF(pr.`reduction_type` = "percentage", IFNULL(pr.`reduction`, 0) * p.`price`, IFNULL(pr.`reduction`, 0))) as price' : '') . '
        FROM `' . _DB_PREFIX_ . 'product` p
        ' . ($enable_lang ? 'LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON pl.`id_product` = p.`id_product`' : '') . '
        ' . (!empty($category) ? 'LEFT JOIN `' . _DB_PREFIX_ . 'category_product` c ON c.`id_product` = p.`id_product`' : '') . '
        ' . ($enable_specific_price ? 'LEFT JOIN `' . _DB_PREFIX_ . 'specific_price` pr ON pr.`id_product` = p.`id_product`' : '') . '
        WHERE 1 AND p.`active` = 1
        ' . (!empty($search) ? '
        AND (pl.`name` LIKE \'%' . pSQL($search) . '%\'
        OR p.`ean13` LIKE \'%' . pSQL($search) . '%\'
        OR p.`isbn` LIKE \'%' . pSQL($search) . '%\'
        OR p.`upc` LIKE \'%' . pSQL($search) . '%\'
        OR p.`reference` LIKE \'%' . pSQL($search) . '%\'
        OR p.`supplier_reference` LIKE \'%' . pSQL($search) . '%\'
        OR pl.`description` LIKE \'%' . pSQL($search) . '%\'
        OR pl.`description_short` LIKE \'%' . pSQL($search) . '%\'
        OR pl.`meta_keywords` LIKE \'%' . pSQL($search) . '%\'
        OR pl.`meta_description` LIKE \'%' . pSQL($search) . '%\'
        )' : '') . '
        ' . (!empty($product) ? ' AND p.`id_product` = ' . $product . '' : '') . '
        ' . $this->buildAttributeConditions($attributes) . '
        ' . $this->buildFeatureConditions($features) . '
        ' . (!empty($include) ? ' AND p.`id_product` IN (' . $include . ')' : '') . '
        ' . ($on_sale == true ? ' AND pr.`reduction` IS NOT NULL' : '') . '
        ' . (!empty($category) ? ' AND c.`id_category` IN (' . $category . ')' : '') . '
        ' . ($enable_having ? 'HAVING' : '') . '
        ' . (!empty($min_price) ? ' `price` > ' . $min_price . '' : '') . '
        ' . (!empty($max_price) ? '' . (!empty($min_price) ? ' AND' : '') . ' `price` < ' . $max_price . '' : '') . '
        ORDER BY ' . $_orderby . ' ' . $_order . '
        LIMIT ' . $start . ', ' . $per_page . '';
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
    }

    public function buildAttributeConditions($attributes)
    {
        if (empty($attributes)) {
            return '';
        }

        $conditions = [];
        foreach ($attributes as $attribute_group_id => $attribute_values) {
            if (empty($attribute_values)) {
                continue;
            }

            // If attribute_values is a string, convert it to an array
            if (is_string($attribute_values)) {
                $attribute_values = explode(',', $attribute_values);
            }

            // Clean and validate attribute values
            $attribute_values = array_map('intval', $attribute_values);
            $attribute_values = array_filter($attribute_values);

            if (empty($attribute_values)) {
                continue;
            }

            $attribute_values_str = implode(',', $attribute_values);
            $conditions[] = 'EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'product_attribute` pa_sub
                JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac_sub ON pac_sub.`id_product_attribute` = pa_sub.`id_product_attribute`
                JOIN `' . _DB_PREFIX_ . 'attribute` a_sub ON a_sub.`id_attribute` = pac_sub.`id_attribute`
                WHERE pa_sub.`id_product` = p.`id_product`
                AND a_sub.`id_attribute_group` = ' . (int)$attribute_group_id . '
                AND pac_sub.`id_attribute` IN (' . $attribute_values_str . ')
            )';
        }

        if (empty($conditions)) {
            return '';
        }

        return ' AND ' . implode(' AND ', $conditions);
    }

    public function buildFeatureConditions($features)
    {
        if (empty($features)) {
            return '';
        }

        $conditions = [];
        foreach ($features as $feature_id => $feature_values) {
            if (empty($feature_values)) {
                continue;
            }

            // If feature_values is a string, convert it to an array
            if (is_string($feature_values)) {
                $feature_values = explode(',', $feature_values);
            }

            // Clean and validate feature values
            $feature_values = array_map('intval', $feature_values);
            $feature_values = array_filter($feature_values);

            if (empty($feature_values)) {
                continue;
            }

            $feature_values_str = implode(',', $feature_values);
            $conditions[] = 'EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'feature_product` fp_sub
                WHERE fp_sub.`id_product` = p.`id_product`
                AND fp_sub.`id_feature` = ' . (int)$feature_id . '
                AND fp_sub.`id_feature_value` IN (' . $feature_values_str . ')
            )';
        }

        if (empty($conditions)) {
            return '';
        }

        return ' AND ' . implode(' AND ', $conditions);
    }

    public function getStockQuantity($id_product)
    {
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `quantity`
            FROM `' . _DB_PREFIX_ . 'stock_available`
            WHERE `id_product` = ' . $id_product . '
            LIMIT 1'
        );
    }

    public function getSalePrice($product)
    {
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_specific_price`, `reduction`, `reduction_type`
            FROM `' . _DB_PREFIX_ . 'specific_price`
            WHERE `id_product` = ' . $product->id . '
            LIMIT 1'
        );
        if (empty($result)) return $product->price;
        if ($result[0]['reduction_type'] != "percentage") return $product->price - $result[0]['reduction'];
        return $product->price * (1 - $result[0]['reduction']);
    }


    public function manage()

    {

        $objects_products = array();

        $objects_products['empty'] = new FluxProduct();
        $params = $this->wsObject->urlFragments;
        $category = $params['category'] ?? null;
        $product = $params['product'] ?? null;
        $page = intval($params['page'] ?? 1);
        $per_page = intval($params['per_page'] ?? 20);
        $search = $params['search'] ?? null;
        $order = $params['order'] ?? null;
        $order_by = $params['order_by'] ?? null;
        $attributes = $params['attributes'] ?? null;
        $features = $params['features'] ?? null;

        if (is_string($attributes) && !empty($attributes)) {
            $attributes = json_decode($attributes, true);
        }

        // Support old params: `attribute` and `attribute_term`.
        if (empty($attributes) && !empty($params['attribute']) && !empty($params['attribute_term'])) {
            $attributes = [
                $params['attribute'] => $params['attribute_term']
            ];
        }

        if (is_string($features) && !empty($features)) {
            $features = json_decode($features, true);
        }

        $include = $params['include'] ?? null;
        $_on_sale = $params['on_sale'] ?? null;
        $on_sale = $_on_sale != null ? (is_bool($_on_sale) ? $_on_sale : json_decode($_on_sale)) : false;
        $min_price = $params['min_price'] ?? null;
        $max_price = $params['max_price'] ?? null;
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        $products = $this->getProducts($category, $page, $per_page, $search, $order, $order_by, $attributes, $features, $include, $on_sale, $min_price, $max_price, $product);
        if (!empty($products)) {
            foreach ($products as $product) {
                $id = $product['id_product'];
                $pro = new FluxProduct($id, false, $id_lang);
                if ($pro->active == null) continue;
                if ($pro->hasAttributes()) {
                    $pro->product_type = 'variable';
                }
                $pro->regular_price = number_format($pro->price, 2, '.', '');
                $pro->sale_price = number_format((float) $pro->getPublicPrice(), 2, '.', '');
                $pro->price = $pro->sale_price;
                $pro->on_sale = $pro->sale_price < $pro->regular_price;
                $pro->stock_quantity = FluxProduct::getRealQuantity($id);
                //Get name product by lang
                $pro->name = FluxUntils::getName($pro->name, $id_lang);
                //Get description by lang
                $pro->description = FluxUntils::getName($pro->description, $id_lang);
                //Get description short by lang
                $pro->description_short = FluxUntils::getName($pro->description_short, $id_lang);
                //Get link rewrite by lang
                $pro->link_rewrite = FluxUntils::getName($pro->link_rewrite, $id_lang);
                //Get delivery_in_stock by lang
                $pro->delivery_in_stock = FluxUntils::getName($pro->delivery_in_stock, $id_lang);
                //Get delivery_out_stock by lang
                $pro->delivery_out_stock = FluxUntils::getName($pro->delivery_out_stock, $id_lang);
                //Get meta_description by lang
                $pro->meta_description = FluxUntils::getName($pro->meta_description, $id_lang);
                //Get meta_title by lang
                $pro->meta_title = FluxUntils::getName($pro->meta_title, $id_lang);
                //Get available_now by lang
                $pro->available_now = FluxUntils::getName($pro->available_now, $id_lang);
                //Get available_later by lang
                $pro->available_later = FluxUntils::getName($pro->available_later, $id_lang);

                $objects_products[] = $pro;
            }
        }

        $this->wsObject->setFieldsToDisplay();
        $result = $this->objOutput->getContent($objects_products, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
        $this->output .= $result;
    }
}
