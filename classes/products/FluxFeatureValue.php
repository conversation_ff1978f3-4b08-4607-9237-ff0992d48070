<?php

/**
 * FluxFeatureValue class
 * Custom class for feature values with count property
 * Extends PrestaShop's FeatureValue class
 */
class FluxFeatureValue extends FeatureValue
{
    public $count = 0;

    protected $webserviceParameters = [
        'objectsNodeName' => 'feature_values',
        'objectNodeName' => 'feature_value',
        'fields' => [
            'id' => [],
            'value' => [],
            'count' => [],
        ],
    ];

    /**
     * Constructor
     *
     * @param int $id Feature value ID
     * @param int $id_lang Language ID
     * @param int $count Count of related products
     */
    public function __construct($id = null, $id_lang = null, $count = 0)
    {
        parent::__construct($id, $id_lang);
        $this->count = $count;
    }
}
