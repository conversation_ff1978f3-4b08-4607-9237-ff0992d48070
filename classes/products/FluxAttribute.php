<?php

class FluxAttribute extends AttributeGroup
{
    public $categoryIds = null;
    public $brandIds = null;
    public $is_visible = false;

    protected $webserviceParameters = [
        'objectsNodeName' => 'product_options',
        'objectNodeName' => 'product_option',
        'fields' => [
            'is_visible' => [],
        ],
        'associations' => [
            'product_option_values' => [
                'resource' => 'product_option_value',
                'fields' => [
                    'id' => [],
                    'name' => [],
                    'count' => [],
                ],
            ],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null, $categoryIds = null, $brandIds = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
        $this->categoryIds = $categoryIds;
        $this->brandIds = $brandIds;
    }

    public function getWsProductOptionValues()
    {
        $result = Db::getInstance()->executeS('
            SELECT a.`id_attribute` AS id, al.`name`
            FROM `' . _DB_PREFIX_ . 'attribute` a
            ' . Shop::addSqlAssociation('attribute', 'a') . '
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_lang` al ON (a.`id_attribute` = al.`id_attribute` AND al.`id_lang` = ' . (int) $this->id_lang . ')
            WHERE a.`id_attribute_group` = ' . (int) $this->id . '
            ORDER BY a.`position` ASC
        ');

        // Create array structure with count
        $attribute_values = [];
        foreach ($result as $row) {
            $count = $this->countProductsByAttributeValue($row['id'], $this->categoryIds, $this->brandIds);
            $attribute_values[] = [
                'id' => $row['id'],
                'name' => $row['name'],
                'count' => $count
            ];
        }

        return $attribute_values;
    }

    /**
     * Count products for a specific attribute value based on categoryIds and brandIds
     *
     * @param int $id_attribute Attribute value ID
     * @param string $categoryIds Comma-separated list of category IDs
     * @param string $brandIds Comma-separated list of brand/manufacturer IDs
     * @return int Number of related products
     */
    public function countProductsByAttributeValue($id_attribute, $categoryIds = null, $brandIds = null)
    {
        // Build query to count products
        $query = '
        SELECT COUNT(DISTINCT p.`id_product`) as product_count
        FROM `' . _DB_PREFIX_ . 'product` p
        JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON pa.`id_product` = p.`id_product`
        JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON pac.`id_product_attribute` = pa.`id_product_attribute`
        WHERE p.`active` = 1
        AND pac.`id_attribute` = ' . (int)$id_attribute;

        // Add category filter if provided
        if (!empty($categoryIds)) {
            $query .= '
            AND EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'category_product` cp
                WHERE cp.`id_product` = p.`id_product`
                AND cp.`id_category` IN (' . pSQL($categoryIds) . ')
            )';
        }

        // Add brand/manufacturer filter if provided
        if (!empty($brandIds)) {
            $query .= '
            AND p.`id_manufacturer` IN (' . pSQL($brandIds) . ')';
        }

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        return isset($result[0]['product_count']) ? (int)$result[0]['product_count'] : 0;
    }
}
