<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxAttribute.php';
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';
class WebserviceSpecificManagementProductOptions implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function getProductOptions($page, $per_page, $id_attribute_group)
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($per_page < 0) {
            $per_page = 1;
        }
        $start = ($page - 1) * $per_page;
        $query = '
        SELECT *
        FROM `' . _DB_PREFIX_ . 'attribute_group` a
        WHERE 1
        ' . (!empty($id_attribute_group) ? ' AND a.`id_attribute_group` = ' . $id_attribute_group . '' : '') . '
        LIMIT ' . $start . ', ' . $per_page . '';
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
    }


    /**
     * Check if an attribute has related products based on categoryIds and brandIds
     *
     * @param int $id_attribute_group Attribute group ID
     * @param string $categoryIds Comma-separated list of category IDs
     * @param string $brandIds Comma-separated list of brand/manufacturer IDs
     * @return bool True if attribute has related products, false otherwise
     */
    public function hasRelatedProducts($id_attribute_group, $categoryIds = null, $brandIds = null)
    {
        // If no filters are provided, just check if the attribute group has any products
        if (empty($categoryIds) && empty($brandIds)) {
            $query = '
            SELECT 1
            FROM `' . _DB_PREFIX_ . 'product` p
            JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON pa.`id_product` = p.`id_product`
            JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON pac.`id_product_attribute` = pa.`id_product_attribute`
            JOIN `' . _DB_PREFIX_ . 'attribute` a ON a.`id_attribute` = pac.`id_attribute`
            WHERE p.`active` = 1
            AND a.`id_attribute_group` = ' . (int)$id_attribute_group . '
            LIMIT 1';

            $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
            return !empty($result);
        }

        // Build query with filters
        $query = '
        SELECT 1
        FROM `' . _DB_PREFIX_ . 'product` p
        JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON pa.`id_product` = p.`id_product`
        JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON pac.`id_product_attribute` = pa.`id_product_attribute`
        JOIN `' . _DB_PREFIX_ . 'attribute` a ON a.`id_attribute` = pac.`id_attribute`
        WHERE p.`active` = 1
        AND a.`id_attribute_group` = ' . (int)$id_attribute_group;

        // Add category filter if provided
        if (!empty($categoryIds)) {
            $query .= '
            AND EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'category_product` cp
                WHERE cp.`id_product` = p.`id_product`
                AND cp.`id_category` IN (' . pSQL($categoryIds) . ')
            )';
        }

        // Add brand/manufacturer filter if provided
        if (!empty($brandIds)) {
            $query .= '
            AND p.`id_manufacturer` IN (' . pSQL($brandIds) . ')';
        }

        // Add LIMIT 1 for better performance since we only need to know if any products exist
        $query .= ' LIMIT 1';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        return !empty($result);
    }

    public function manage()
    {
        $objects_product_options = array();

        $objects_product_options['empty'] = new FluxAttribute();

        $params = $this->wsObject->urlFragments;
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        $page = intval($params['page'] ?? 1);
        $per_page = intval($params['per_page'] ?? 20);
        $id_attribute_group = $params['id_attribute_group'] ?? null;

        $categoryIds = $params['categoryIds'] ?? null;
        $brandIds = $params['brandIds'] ?? null;

        $product_option_values = $this->getProductOptions($page, $per_page, $id_attribute_group);
        foreach ($product_option_values as $option_value) {
            $id = $option_value['id_attribute_group'];
            $item = new FluxAttribute($id, $id_lang, null, $categoryIds, $brandIds);
            $item->name = FluxUntils::getName($item->name, $id_lang);
            $item->public_name = FluxUntils::getName($item->public_name, $id_lang);

            // Set is_visible based on whether there are related products
            $item->is_visible = $this->hasRelatedProducts($id, $categoryIds, $brandIds);

            $objects_product_options[] = $item;
        }
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_product_options, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
