<?php

/**
 * FluxAttributeValue class
 * Custom class for attribute values with count property
 * Extends PrestaShop's ProductAttributeCore class
 */
class FluxAttributeValue extends ProductAttributeCore
{
    public $count = 0;

    protected $webserviceParameters = [
        'objectsNodeName' => 'product_option_values',
        'objectNodeName' => 'product_option_value',
        'fields' => [
            'id' => [],
            'name' => [],
            'count' => [],
        ],
    ];

    /**
     * Constructor
     *
     * @param int $id Attribute value ID
     * @param int $id_lang Language ID
     * @param int $count Count of related products
     */
    public function __construct($id = null, $id_lang = null, $count = 0)
    {
        parent::__construct($id, $id_lang);
        $this->count = $count;
    }
}
