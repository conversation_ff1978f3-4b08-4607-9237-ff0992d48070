<?php

class FluxPriceRange extends ObjectModel
{
    public $id = 1; // We need an ID for ObjectModel
    public $min_price;
    public $max_price;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = array(
        'table' => 'product',
        'primary' => 'id_product',
        'fields' => array(
            'min_price' => array('type' => self::TYPE_STRING),
            'max_price' => array('type' => self::TYPE_STRING),
        ),
    );

    protected $webserviceParameters = [
        'objectsNodeName' => 'price_ranges',
        'objectNodeName' => 'price_range',
        'fields' => [
            'min_price' => [],
            'max_price' => [],
        ],
    ];

    public function __construct($id = null, $idLang = null, $idShop = null)
    {
        parent::__construct($id, $idLang, $idShop);
    }
}
