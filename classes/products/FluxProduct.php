<?php
class FluxProduct extends Product
{
    public $sale_price = null;
    public $regular_price = null;
    public $stock_quantity = null;
    public $quantity = 0;

    protected $webserviceParameters = [
        'objectMethods' => [
            'add' => 'addWs',
            'update' => 'updateWs',
        ],
        'objectNodeNames' => 'products',
        'fields' => [
            'id_default_image' => [
                'getter' => 'getCoverWs',
                'setter' => 'setCoverWs',
                'xlink_resource' => [
                    'resourceName' => 'images',
                    'subResourceName' => 'products',
                ],
            ],
            'quantity' => [
                'getter' => false,
                'setter' => false,
            ],
            'regular_price' => [
                'getter' => false,
                'setter' => false,
            ],
            'sale_price' => [
                'getter' => false,
                'setter' => false,
            ],
            'stock_quantity' => [
                'getter' => false,
                'setter' => false,
            ]
        ],
        'associations' => [
            'images' => [
                'resource' => 'image',
                'fields' => ['id' => []],
            ],
            'product_option_values' => [
                'resource' => 'product_option_value',
                'fields' => [
                    'id' => ['required' => true],
                    'name' => [],
                    'group_id' => [],
                    'group_name' => [],
                ],
            ],
            'product_features' => [
                'resource' => 'product_feature',
                'fields' => [
                    'id' => ['required' => true],
                    'id_feature' => [
                        'required' => true,
                        'xlink_resource' => 'product_feature_values',
                    ],
                    'feature_name' => [],
                    'value' => [],
                ],
            ],
            'product_bundle' => [
                'resource' => 'product',
                'api' => 'products',
                'fields' => [
                    'id' => ['required' => true],
                    'id_product_attribute' => [],
                    'quantity' => [],
                ],
            ],
        ],
    ];

    public function getWsProductOptionValues()
    {
        $result = Db::getInstance()->executeS(
            'SELECT DISTINCT pac.id_attribute as id, atl.name as name, att.id_attribute_group as group_id, atgl.name as group_name
            FROM `' . _DB_PREFIX_ . 'product_attribute` pa
            ' . Shop::addSqlAssociation('product_attribute', 'pa') . '
            LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON (pac.id_product_attribute = pa.id_product_attribute)
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_lang` atl ON (pac.id_attribute = atl.id_attribute)
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute` att ON (att.id_attribute = pac.id_attribute)
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` atgl ON (atgl.id_attribute_group = att.id_attribute_group)
            WHERE
            ' . (!empty($this->id_lang) ? 'atl.id_lang=' . $this->id_lang . ' AND atgl.id_lang=' . $this->id_lang . ' AND' : '') . ' pa.id_product = ' . $this->id . '
            ORDER BY att.position ASC'
        );
        return $result;
    }

    public function getWsProductFeatures()
    {
        $result = Db::getInstance()->executeS(
            '
            SELECT fp.`id_feature_value` as id, fp.`id_feature`, fl.`name` as feature_name, fvl.`value`
            FROM `' . _DB_PREFIX_ . 'feature_product` fp
            LEFT JOIN `' . _DB_PREFIX_ . 'feature_value_lang` fvl ON (fp.`id_feature_value` = fvl.`id_feature_value`)
            LEFT JOIN `' . _DB_PREFIX_ . 'feature_lang` fl ON (fp.`id_feature` = fl.`id_feature`)
            WHERE fp.`id_product` = ' . (int)$this->id . '
            ' . (!empty($this->id_lang) ? 'AND fvl.`id_lang` = ' . (int)$this->id_lang . ' AND fl.`id_lang` = ' . (int)$this->id_lang : '') . '
            ORDER BY fp.`id_feature` ASC
            '
        );
        return $result;
    }
}
