<?php

use PrestaShopBundle\Translation\Translator;

class FluxCombination extends CombinationCore
{
    protected $webserviceParameters = [
        'objectNodeName' => 'combination',
        'objectsNodeName' => 'combinations',
        'fields' => [
            'id_product' => ['required' => true, 'xlink_resource' => 'products'],
        ],
        'associations' => [
            'product_option_values' => ['resource' => 'product_option_value', 'fields' => [
                'id' => ['required' => true],
                'name' => [],
                'group_id' => [],
                'group_name' => [],
            ],],
            'stock_availables' => ['resource' => 'stock_available', 'fields' => [
                'id' => ['required' => false],
                'quantity' => [],
            ]],
            'images' => ['resource' => 'image', 'fields' => [
                'id' => ['required' => false],
            ]]
        ],
    ];

    public function getWsProductOptionValues()
    {
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
			SELECT DISTINCT a.id_attribute AS id, atl.name as name, att.id_attribute_group as group_id, atgl.name as group_name
			FROM `' . _DB_PREFIX_ . 'product_attribute_combination` a
			' . Shop::addSqlAssociation('attribute', 'a') . '
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_lang` atl ON (a.id_attribute = atl.id_attribute)
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute` att ON (att.id_attribute = a.id_attribute)
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` atgl ON (atgl.id_attribute_group = att.id_attribute_group)
			WHERE 
            ' . (!empty($this->id_lang) ? 'atl.id_lang=' . $this->id_lang . ' AND atgl.id_lang=' . $this->id_lang . ' AND' : '') . ' a.id_product_attribute = ' . (int) $this->id . '
            ORDER BY att.position ASC'
        );
        return $result;
    }

    public function getWsImages()
    {
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT *, i.id_image as id
            FROM `' . _DB_PREFIX_ . 'image` i
            LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_image` pai ON (pai.id_image = i.id_image)
            WHERE pai.`id_product_attribute` = \'' . $this->id . '\' AND i.`id_product` = \'' . $this->id_product . '\'
            '
        );
        return $result;
    }

    public function getWsStockAvailables()
    {
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT *, id_product as id
            FROM `' . _DB_PREFIX_ . 'stock_available`
            WHERE `id_product_attribute` = \'' . $this->id . '\'
            '
        );
        return $result;
    }

    public function __construct(?int $id = null, ?int $id_lang = null, ?int $id_shop = null, ?Translator $translator = null)
    {
        parent::__construct($id, $id_lang, $id_shop, $translator);
    }
}
