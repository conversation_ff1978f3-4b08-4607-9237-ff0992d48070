<?php
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxPriceRange.php';

class WebserviceSpecificManagementProductPriceRange implements WebserviceSpecificManagementInterface
{
    /** @var WebserviceOutputBuilder */
    protected $objOutput;
    protected $output;

    /** @var WebserviceRequest */
    protected $wsObject;

    public function getWsObject()
    {
        return $this->wsObject;
    }

    public function getObjectOutput()
    {
        return $this->objOutput;
    }

    public function getContent()
    {
        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }

    public function setWsObject(WebserviceRequestCore $obj)
    {
        $this->wsObject = $obj;
        return $this;
    }

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)
    {
        $this->objOutput = $obj;
        return $this;
    }

    /**
     * Get the min and max price of all active products
     *
     * @return array Array with min and max price
     */
    public function getPriceRange()
    {
        // Simple query to get min and max prices of all active products
        $query = '
        SELECT
            MIN(p.`price`) as min_price,
            MAX(p.`price`) as max_price
        FROM `' . _DB_PREFIX_ . 'product` p
        WHERE p.`active` = 1';

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        // Default values
        $min_price = '0.00';
        $max_price = '0.00';

        if (!empty($result) && isset($result[0])) {
            // Make sure we're getting numeric values
            $min_value = isset($result[0]['min_price']) ? (float)$result[0]['min_price'] : 0;
            $max_value = isset($result[0]['max_price']) ? (float)$result[0]['max_price'] : 0;

            // Format the prices
            $min_price = number_format($min_value, 2, '.', '');
            $max_price = number_format($max_value, 2, '.', '');
        }

        return ['min_price' => $min_price, 'max_price' => $max_price];
    }

    public function manage()
    {
        // Create a standard object array for the response
        $objects = array();

        // Create an empty object as required by PrestaShop WebService
        $objects['empty'] = new FluxPriceRange();

        // Get the price range
        $range = $this->getPriceRange();

        // Create a price range object
        $price_range = new FluxPriceRange();
        $price_range->min_price = $range['min_price'];
        $price_range->max_price = $range['max_price'];

        // Add the price range object to the response
        $objects[] = $price_range;

        // Set fields to display (required by PrestaShop WebService)
        $this->wsObject->setFieldsToDisplay();

        // Generate the output using PrestaShop's standard content generator
        $this->output .= $this->objOutput->getContent($objects, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
