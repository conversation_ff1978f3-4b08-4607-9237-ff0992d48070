<?php
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxFeatureValue.php';

class WebserviceSpecificManagementProductFeatureValues implements WebserviceSpecificManagementInterface
{
    /** @var WebserviceOutputBuilder */
    protected $objOutput;
    protected $output;

    /** @var WebserviceRequest */
    protected $wsObject;

    public function setUrlSegment($segments)
    {
        $this->urlSegment = $segments;
        return $this;
    }

    public function getUrlSegment()
    {
        return $this->urlSegment;
    }

    public function getWsObject()
    {
        return $this->wsObject;
    }

    public function getObjectOutput()
    {
        return $this->objOutput;
    }

    /**
     * This must be return a string with specific values as WebserviceRequest expects.
     *
     * @return string
     */
    public function getContent()
    {
        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }

    public function setWsObject(WebserviceRequestCore $obj)
    {
        $this->wsObject = $obj;
        return $this;
    }

    /**
     * @param WebserviceOutputBuilderCore $obj
     * @return WebserviceSpecificManagementInterface
     */
    public function setObjectOutput(WebserviceOutputBuilderCore $obj)
    {
        $this->objOutput = $obj;
        return $this;
    }

    public function getFeatureValues($id_feature = null, $id_feature_value = null, $page = 1, $per_page = 20)
    {
        $limit = '';
        if ($per_page > 0) {
            $limit = ' LIMIT ' . (($page - 1) * $per_page) . ',' . $per_page;
        }

        $sql = '
            SELECT fv.`id_feature_value`
            FROM `' . _DB_PREFIX_ . 'feature_value` fv
            ' . ($id_feature ? 'WHERE fv.`id_feature` = ' . (int)$id_feature : '') . '
            ' . ($id_feature_value ? ($id_feature ? 'AND' : 'WHERE') . ' fv.`id_feature_value` = ' . (int)$id_feature_value : '') . '
            ORDER BY fv.`id_feature_value` ASC
            ' . $limit;

        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($sql);
    }

    /**
     * Count products for a specific feature value based on categoryIds and brandIds
     *
     * @param int $id_feature_value Feature value ID
     * @param string $categoryIds Comma-separated list of category IDs
     * @param string $brandIds Comma-separated list of brand/manufacturer IDs
     * @return int Number of related products
     */
    public function countProductsByFeatureValue($id_feature_value, $categoryIds = null, $brandIds = null)
    {
        // Build query to count products
        $query = '
        SELECT COUNT(DISTINCT p.`id_product`) as product_count
        FROM `' . _DB_PREFIX_ . 'product` p
        JOIN `' . _DB_PREFIX_ . 'feature_product` fp ON fp.`id_product` = p.`id_product`
        WHERE p.`active` = 1
        AND fp.`id_feature_value` = ' . (int)$id_feature_value;

        // Add category filter if provided
        if (!empty($categoryIds)) {
            $query .= '
            AND EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'category_product` cp
                WHERE cp.`id_product` = p.`id_product`
                AND cp.`id_category` IN (' . pSQL($categoryIds) . ')
            )';
        }

        // Add brand/manufacturer filter if provided
        if (!empty($brandIds)) {
            $query .= '
            AND p.`id_manufacturer` IN (' . pSQL($brandIds) . ')';
        }

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        return isset($result[0]['product_count']) ? (int)$result[0]['product_count'] : 0;
    }

    public function getLanguageId($lang)
    {
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_lang`
            FROM `' . _DB_PREFIX_ . 'lang`
            WHERE `iso_code` = \'' . $lang . '\'
            LIMIT 1'
        );
    }

    public function manage()
    {
        $objects_feature_values = array();
        $objects_feature_values['empty'] = new FluxFeatureValue();

        $params = $this->wsObject->urlFragments;
        $id_feature = $params['id_feature'] ?? null;
        $id_feature_value = $params['id_feature_value'] ?? null;
        $page = intval($params['page'] ?? 1);
        $per_page = intval($params['per_page'] ?? 20);
        $lang = $params['lang'] ?? 'en';
        $categoryIds = $params['categoryIds'] ?? null;
        $brandIds = $params['brandIds'] ?? null;
        $lang = $this->getLanguageId($lang) ?? [];
        $id_lang = count($lang) > 0 ? strval($lang[0]['id_lang']) : null;

        $feature_values = $this->getFeatureValues($id_feature, $id_feature_value, $page, $per_page);
        foreach ($feature_values as $feature_value) {
            $id = $feature_value['id_feature_value'];

            // Count products with this feature value
            $count = $this->countProductsByFeatureValue($id, $categoryIds, $brandIds);

            // Create a FluxFeatureValue object with the count
            $item = new FluxFeatureValue($id, $id_lang, $count);

            $objects_feature_values[] = $item;
        }

        // Set fields to display based on the empty object's webservice parameters
        $this->wsObject->resourceConfiguration = $objects_feature_values['empty']->getWebserviceParameters();
        $this->wsObject->setFieldsToDisplay();
        $this->output .= $this->objOutput->getContent($objects_feature_values, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
