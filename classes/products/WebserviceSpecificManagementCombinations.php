<?php
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxCombination.php';
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';

use PrestaShopBundle\Translation\Translator;

class WebserviceSpecificManagementCombinations implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function getCombinations($id_product, $variation)
    {
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_product_attribute`
            FROM `' . _DB_PREFIX_ . 'product_attribute` pa
            WHERE 1
            ' . (!empty($id_product) ? ' AND pa.`id_product` = ' . $id_product . '' : '') . '
            ' . (!empty($variation) ? ' AND pa.`id_product_attribute` = ' . $variation . '' : '') . ''
        );
    }

    public function getTaxStockQuantity($id_product, $id_product_attribute)
    {
        $query = new DbQuery();
        $query->select('SUM(quantity)');
        $query->from('stock_available');

        // if null, it's a product without attributes
        if ($id_product !== null) {
            $query->where('id_product = ' . (int) $id_product);
        }

        $query->where('id_product_attribute = ' . (int) $id_product_attribute);
        $query = StockAvailable::addSqlShopRestriction($query, 1);
        $result = (int) Db::getInstance(_PS_USE_SQL_SLAVE_)->getValue($query);
        return $result;
    }

    public function isExistIdShop($id_shop_default)
    {
        if (empty($id_shop_default)) return false;
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_shop`
            FROM `' . _DB_PREFIX_ . 'shop`
            WHERE `id_shop` = ' . $id_shop_default . '
            LIMIT 1'
        );
        if (empty($result)) return false;
        return true;
    }

    public function getSalePrice($product, $combination)
    {
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT  `id_specific_price`, `reduction`, `reduction_type`
            FROM `' . _DB_PREFIX_ . 'specific_price`
            WHERE `id_product` = ' . $product->id . '
            LIMIT 1'
        );
        if (empty($result)) return $combination->price;
        if ($result[0]['reduction_type'] != "percentage") return $combination->price - $result[0]['reduction'];
        return ($combination->price) * (1 - $result[0]['reduction']);
    }


    public function manage()

    {
        $objects_combination = array();
        $objects_combination['empty'] = new FluxCombination();
        $params = $this->wsObject->urlFragments;
        $product = $params['product'] ?? null;
        $variation = $params['variation'] ?? null;
        $combinations = $this->getCombinations($product, $variation);
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        foreach ($combinations as $item) {
            $combination = new FluxCombination($item['id_product_attribute'], $id_lang);
            if ($combination->id_product != $product && $product != null) continue;
            if (Configuration::get('CUSTOMAPI_ENABLE_TAX', true) == true) {
                $combination->price = number_format((float) (Product::getPriceStatic($combination->id_product, true, $combination->id, 2, null, false, false)), 2, '.', '');
            } else {
                $combination->price = number_format((float) ($product->price + $combination->price), 2, '.', '');
            }
            if (Configuration::get('CUSTOMAPI_ENABLE_TAX', true) == true) {
                $combination->wholesale_price = number_format((float) (Product::getPriceStatic($combination->id_product, true, $combination->id, 2)), 2, '.', '');
            } else {
                $combination->wholesale_price = number_format((float) ($this->getSalePrice($product, $combination)), 2, '.', '');
            }
            $objects_combination[] = $combination;
        }

        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_combination, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
