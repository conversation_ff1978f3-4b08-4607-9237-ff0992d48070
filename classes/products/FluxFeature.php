<?php

class FluxFeature extends Feature
{
    public $categoryIds = null;
    public $brandIds = null;
    public $is_visible = false;

    protected $webserviceParameters = [
        'objectNodeName' => 'feature',
        'objectsNodeName' => 'features',
        'fields' => [
            'is_visible' => [],
        ],
        'associations' => [
            'feature_values' => [
                'resource' => 'feature_value',
                'fields' => [
                    'id' => ['required' => true],
                    'value' => [],
                    'count' => [],
                ],
            ],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null, $categoryIds = null, $brandIds = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
        $this->categoryIds = $categoryIds;
        $this->brandIds = $brandIds;
    }

    public function getWsFeatureValues()
    {
        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT fv.id_feature_value as id, fvl.value
            FROM `' . _DB_PREFIX_ . 'feature_value` fv
            LEFT JOIN `' . _DB_PREFIX_ . 'feature_value_lang` fvl ON (fv.id_feature_value = fvl.id_feature_value)
            WHERE fv.id_feature = ' . (int) $this->id . '
            ' . (!empty($this->id_lang) ? 'AND fvl.id_lang = ' . (int) $this->id_lang : '') . '
            ORDER BY fvl.value ASC'
        );

        // Create array structure with count
        $feature_values = [];
        foreach ($result as $row) {
            $count = $this->countProductsByFeatureValue($row['id'], $this->categoryIds, $this->brandIds);
            $feature_values[] = [
                'id' => $row['id'],
                'value' => $row['value'],
                'count' => $count
            ];
        }

        return $feature_values;
    }

    /**
     * Count products for a specific feature value based on categoryIds and brandIds
     *
     * @param int $id_feature_value Feature value ID
     * @param string $categoryIds Comma-separated list of category IDs
     * @param string $brandIds Comma-separated list of brand/manufacturer IDs
     * @return int Number of related products
     */
    public function countProductsByFeatureValue($id_feature_value, $categoryIds = null, $brandIds = null)
    {
        // Build query to count products
        $query = '
        SELECT COUNT(DISTINCT p.`id_product`) as product_count
        FROM `' . _DB_PREFIX_ . 'product` p
        JOIN `' . _DB_PREFIX_ . 'feature_product` fp ON fp.`id_product` = p.`id_product`
        WHERE p.`active` = 1
        AND fp.`id_feature_value` = ' . (int)$id_feature_value;

        // Add category filter if provided
        if (!empty($categoryIds)) {
            $query .= '
            AND EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'category_product` cp
                WHERE cp.`id_product` = p.`id_product`
                AND cp.`id_category` IN (' . pSQL($categoryIds) . ')
            )';
        }

        // Add brand/manufacturer filter if provided
        if (!empty($brandIds)) {
            $query .= '
            AND p.`id_manufacturer` IN (' . pSQL($brandIds) . ')';
        }

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        return isset($result[0]['product_count']) ? (int)$result[0]['product_count'] : 0;
    }
}
