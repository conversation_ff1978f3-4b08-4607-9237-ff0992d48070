<?php
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';
include_once _PS_MODULE_DIR_ . 'customapi/classes/products/FluxAttributeValue.php';

class WebserviceSpecificManagementProductOptionValues implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function getProductOptionValues($page, $per_page, $id_attribute_group)
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($per_page < 0) {
            $per_page = 1;
        }
        $start = ($page - 1) * $per_page;
        $query = '
        SELECT *
        FROM `' . _DB_PREFIX_ . 'attribute` a
        WHERE 1
        ' . (!empty($id_attribute_group) ? ' AND a.`id_attribute_group` = ' . $id_attribute_group . '' : '') . '
        LIMIT ' . $start . ', ' . $per_page . '';
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
    }

    /**
     * Count products for a specific attribute value based on categoryIds and brandIds
     *
     * @param int $id_attribute Attribute value ID
     * @param string $categoryIds Comma-separated list of category IDs
     * @param string $brandIds Comma-separated list of brand/manufacturer IDs
     * @return int Number of related products
     */
    public function countProductsByAttributeValue($id_attribute, $categoryIds = null, $brandIds = null)
    {
        // Build query to count products
        $query = '
        SELECT COUNT(DISTINCT p.`id_product`) as product_count
        FROM `' . _DB_PREFIX_ . 'product` p
        JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON pa.`id_product` = p.`id_product`
        JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON pac.`id_product_attribute` = pa.`id_product_attribute`
        WHERE p.`active` = 1
        AND pac.`id_attribute` = ' . (int)$id_attribute;

        // Add category filter if provided
        if (!empty($categoryIds)) {
            $query .= '
            AND EXISTS (
                SELECT 1 FROM `' . _DB_PREFIX_ . 'category_product` cp
                WHERE cp.`id_product` = p.`id_product`
                AND cp.`id_category` IN (' . pSQL($categoryIds) . ')
            )';
        }

        // Add brand/manufacturer filter if provided
        if (!empty($brandIds)) {
            $query .= '
            AND p.`id_manufacturer` IN (' . pSQL($brandIds) . ')';
        }

        $result = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);

        return isset($result[0]['product_count']) ? (int)$result[0]['product_count'] : 0;
    }


    public function manage()
    {

        $objects_product_option_values = array();
        $objects_product_option_values['empty'] = new FluxAttributeValue();
        $params = $this->wsObject->urlFragments;
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        $page = intval($params['page'] ?? 1);
        $per_page = intval($params['per_page'] ?? 20);
        $id_attribute_group = $params['id_attribute_group'] ?? null;
        $categoryIds = $params['categoryIds'] ?? null;
        $brandIds = $params['brandIds'] ?? null;
        $product_option_values = $this->getProductOptionValues($page, $per_page, $id_attribute_group);
        foreach ($product_option_values as $option_value) {
            $id = $option_value['id_attribute'];

            // Count products with this attribute value
            $count = $this->countProductsByAttributeValue($id, $categoryIds, $brandIds);

            // Create a FluxAttributeValue object with the count
            $item = new FluxAttributeValue($id, $id_lang, $count);
            // Set the name property if we got it from the original item
            if (!empty($name)) {
                $item->name = $name;
            }

            $objects_product_option_values[] = $item;
        }
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_product_option_values, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
