<?php

include_once _PS_MODULE_DIR_ . 'customapi/classes/categories/FluxCategory.php';
include_once _PS_MODULE_DIR_ . 'customapi/translations/untils.php';

class WebserviceSpecificManagementCategories implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;


    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    public function getCategories($page, $per_page, $parent)
    {
        if ($page < 1) {
            $page = 1;
        }
        if ($per_page < 0) {
            $per_page = 1;
        }
        $depthQuery = 'c.`level_depth` > 1';
        $parentQuery = (!empty($parent) ? ' AND c.`id_parent` = ' . $parent . '' : '');
        if (isset($parent) && (int)$parent == 0) {
            $depthQuery = 'c.`level_depth` = 2';
            $parentQuery = '';
        }
        $start = ($page - 1) * $per_page;
        $query = '
        SELECT *
        FROM `' . _DB_PREFIX_ . 'category` c
        WHERE 1 AND '. $depthQuery .' AND c.`active` = 1
        ' . $parentQuery . '
        ORDER BY c.`level_depth` asc
        LIMIT '.$start.', '.$per_page.'';
        return Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS($query);
    }


    public function manage()
    {

        $objects_categories = array();

        $objects_categories['empty'] = new FluxCategory();

        $params = $this->wsObject->urlFragments;
        $page = intval($params['page'] ?? 1);
        $per_page = intval($params['per_page'] ?? 20);
        $parent = (isset($params['parent']) && is_numeric($params['parent'])) ? $params['parent'] : null;
        $id_lang = FluxUntils::getIdLangByLocale($params['lang'] ?? null);
        $categories = $this->getCategories($page, $per_page, $parent);
        foreach ($categories as $category) {
            $id = $category['id_category'];
            $item = new FluxCategory($id);
            if ($item->level_depth == 2) {
                $item->id_parent = 0;
            }
            $item->has_children = FluxCategory::hasChildren($item->id, $id_lang);
            $item->name = FluxUntils::getName($item->name, $id_lang);
            $item->description = FluxUntils::getName($item->description, $id_lang);
            $item->link_rewrite = FluxUntils::getName($item->link_rewrite, $id_lang);
            $item->meta_keywords = FluxUntils::getName($item->meta_keywords, $id_lang);
            $item->meta_description = FluxUntils::getName($item->meta_description, $id_lang);
            if (isset($item->additional_description)) {
                $item->additional_description = FluxUntils::getName($item->additional_description, $id_lang);
            }
            $item->meta_title = FluxUntils::getName($item->meta_title, $id_lang);
            $objects_categories[] = $item;
        }
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_categories, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}