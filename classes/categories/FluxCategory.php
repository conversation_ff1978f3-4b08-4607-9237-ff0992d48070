<?php

class FluxCategory extends CategoryCore
{
    public $has_children;

    protected $webserviceParameters = [
        'objectNodeName' => 'category',
        'objectsNodeName' => 'categories',
        'hidden_fields' => ['nleft', 'nright', 'groupBox'],
        'fields' => [
            'id_parent' => ['xlink_resource' => 'categories'],
            'has_children' => [],
            'nb_products_recursive' => ['getter' => 'getWsNbProductsRecursive', 'setter' => false],
        ],
        'associations' => [],
    ];

    public function __construct($idCategory = null, $idLang = null, $idShop = null)
    {
        parent::__construct($idCategory, $idLang, $idShop);
    }
}
