<?php

class WebserviceSpecificManagementDebug implements WebserviceSpecificManagementInterface
{

    /** @var WebserviceOutputBuilder */

    protected $objOutput;

    protected $output;



    /** @var WebserviceRequest */

    protected $wsObject;



    public function setUrlSegment($segments)

    {

        $this->urlSegment = $segments;

        return $this;
    }



    public function getUrlSegment()

    {

        return $this->urlSegment;
    }

    public function getWsObject()

    {

        return $this->wsObject;
    }



    public function getObjectOutput()

    {

        return $this->objOutput;
    }



    /**

     * This must be return a string with specific values as WebserviceRequest expects.

     *

     * @return string

     */

    public function getContent()

    {

        return $this->objOutput->getObjectRender()->overrideContent($this->output);
    }



    public function setWsObject(WebserviceRequestCore $obj)

    {

        $this->wsObject = $obj;

        return $this;
    }



    /**

     * @param WebserviceOutputBuilderCore $obj

     * @return WebserviceSpecificManagementInterface

     */

    public function setObjectOutput(WebserviceOutputBuilderCore $obj)

    {

        $this->objOutput = $obj;

        return $this;
    }

    function getProductTitleDesription($id_product_attribute, $id_lang)
    {
        $product_attributes = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
            '
            SELECT DISTINCT pa.`id_product_attribute` as id, agl.`name` as label,  al.`name` as value
            FROM `' . _DB_PREFIX_ . 'product_attribute` pa
            LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON pac.`id_product_attribute` = pa.`id_product_attribute`
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute` a ON a.`id_attribute` = pac.`id_attribute`
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_lang` al ON al.`id_attribute` = a.`id_attribute` AND al.`id_lang` = ' . $id_lang .'
            LEFT JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` agl ON agl.`id_attribute_group` = a.`id_attribute_group` AND agl.`id_lang` = ' . $id_lang .'
            WHERE pa.`id_product_attribute` =  ' . $id_product_attribute . '
            '
        );
        if (empty($product_attributes)) return '';
        $title = "";
        foreach ($product_attributes as $product_attribute) {
            if (empty($title)) {
                $title = $product_attribute['label'] . ": " . $product_attribute['value'];
            } else {
                $title = $title . " - " . $product_attribute['label'] . ": " . $product_attribute['value'];
            }
        }
        return $title;
    }

    public function manage()

    {
        $objects_products = array();
        $objects_products['empty'] = new Combination();
        $id_product_attribute = $this->wsObject->urlFragments['id_product_attribute'] ?? null;
        $id_lang = $this->wsObject->urlFragments['id_lang'] ?? 1;
        $product_attribute = new Combination($id_product_attribute);
        $product_attribute->reference = $this->getProductTitleDesription($id_product_attribute, $id_lang);
        $objects_products[] = $product_attribute;


        // for ($i = 0; $i < count($matches); $i++) {
        //     $product = new Product($matches[$i]);
        //     $product->quantity = $quantity[$i];
        //     $product->id_supplier = $attribute[$i];
        //     if ($attribute[$i] != '-1') {
        //         $combination = new Combination($attribute[$i]);
        //         $product->price = number_format((float) ($product->price + $combination->price), 2, '.', '');
        //     }
        //     $products[] = $product;
        // }
        // $objects_products[] = $this->insertOrder($order, $products);
        $this->_resourceConfiguration = $objects_products['empty']->getWebserviceParameters();
        // $this->_resourceConfiguration = $products['empty']->getWebserviceParameters();
        $this->wsObject->setFieldsToDisplay();

        $this->output .= $this->objOutput->getContent($objects_products, null, $this->wsObject->fieldsToDisplay, $this->wsObject->depth, WebserviceOutputBuilder::VIEW_LIST, false);
    }
}
