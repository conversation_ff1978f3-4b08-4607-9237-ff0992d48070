<?php

class FluxUntils
{

    static public function getName($value, $id_lang)
    {
        try {
            if (gettype($value) == 'array') {
                if (!empty($value[$id_lang])) {
                    return $value[$id_lang];
                }
                return reset($value);
            }
            return $value;
        } catch (Exception $e) {
            return $value;
        }
    }


    static public function getIdLangByLocale($value)
    {
        if (!$value) {
            return Configuration::get('PS_LANG_DEFAULT');
        }
        try {
            $id_lang = null;
            if (function_exists('Language::getIdByLocale')) {
                $id_lang = Language::getIdByLocale($value);
            } else {
                $langs = Db::getInstance(_PS_USE_SQL_SLAVE_)->executeS(
                    '
                    SELECT  `id_lang`
                    FROM `' . _DB_PREFIX_ . 'lang`
                    WHERE `iso_code` = \'' . $value . '\'
                    LIMIT 1'
                ) ?? [];
                $id_lang = count($langs) > 0 ? strval($langs[0]['id_lang']) : null;
            }
            return is_numeric($id_lang) ? $id_lang : Configuration::get('PS_LANG_DEFAULT');
        } catch (Exception $e) {
            return Configuration::get('PS_LANG_DEFAULT');
        }
    }
}
